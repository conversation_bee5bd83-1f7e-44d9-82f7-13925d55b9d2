"""
AI system for automated combat decisions in D&D Combat Simulator
"""

from typing import List, Optional, <PERSON><PERSON>
import random

from src.core.character import Character
from src.core.monster import Monster, Attack
from src.core.combat import CombatEngine, ActionType
from src.core.abilities import AbilityType

class CombatAI:
    """Basic AI for making combat decisions."""
    
    def __init__(self, combat_engine: CombatEngine):
        self.combat_engine = combat_engine
        
    def choose_action(self, actor: Character) -> Tu<PERSON>[ActionType, Optional[Character], Optional[Attack]]:
        """
        Choose an action for the given actor.
        
        Returns:
            Tuple of (action_type, target, attack). Target and attack may be None.
        """
        if not actor.can_take_actions():
            return ActionType.DODGE, None, None
            
        # Get potential targets
        targets = self.get_valid_targets(actor)
        
        if not targets:
            # No valid targets, take defensive action
            return ActionType.DODGE, None, None
            
        # Choose target (prioritize low HP enemies)
        target = self.choose_target(targets)
        
        # If this is a monster with attacks, use them
        if isinstance(actor, Monster) and actor.attacks:
            attack = self.choose_attack(actor, target)
            return ActionType.ATTACK, target, attack
        else:
            # For characters without defined attacks, use basic attack
            return ActionType.ATTACK, target, None
            
    def get_valid_targets(self, actor: Character) -> List[Character]:
        """Get list of valid targets for the actor."""
        targets = []
        
        for participant in self.combat_engine.participants:
            if participant == actor or not participant.is_alive():
                continue
                
            # For now, assume monsters target non-monsters and vice versa
            if isinstance(actor, Monster) and not isinstance(participant, Monster):
                targets.append(participant)
            elif not isinstance(actor, Monster) and isinstance(participant, Monster):
                targets.append(participant)
                
        return targets
        
    def choose_target(self, targets: List[Character]) -> Character:
        """
        Choose the best target from available options.
        
        Priority:
        1. Lowest current HP
        2. Highest threat (damage potential)
        3. Random if tied
        """
        if not targets:
            return None
            
        # Sort by current HP (ascending), then by max HP (descending for threat)
        targets_sorted = sorted(targets, key=lambda t: (t.hit_points.current, -t.hit_points.maximum))
        
        # Add some randomness - choose from top 2 targets if available
        top_targets = targets_sorted[:min(2, len(targets_sorted))]
        return random.choice(top_targets)
        
    def choose_attack(self, monster: Monster, target: Character) -> Attack:
        """Choose the best attack for a monster against a target."""
        available_attacks = monster.get_available_attacks()
        
        if not available_attacks:
            return None
            
        # For now, prefer melee attacks if in range, otherwise ranged
        melee_attacks = [a for a in available_attacks if a.range_normal is None]
        ranged_attacks = [a for a in available_attacks if a.range_normal is not None]
        
        # Simple distance check - assume melee range for now
        # In a full implementation, you'd track positions
        if melee_attacks:
            # Choose highest damage melee attack
            return max(melee_attacks, key=lambda a: self.estimate_damage(a))
        elif ranged_attacks:
            return max(ranged_attacks, key=lambda a: self.estimate_damage(a))
        else:
            return available_attacks[0]
            
    def estimate_damage(self, attack: Attack) -> float:
        """Estimate average damage for an attack."""
        # Parse dice string to estimate damage
        try:
            if 'd' in attack.damage_dice:
                parts = attack.damage_dice.split('d')
                dice_count = int(parts[0])
                dice_sides = int(parts[1])
                average_roll = (dice_sides + 1) / 2
                return dice_count * average_roll + attack.damage_bonus
        except:
            pass
            
        return attack.damage_bonus
        
    def should_use_special_ability(self, monster: Monster, ability_name: str) -> bool:
        """Determine if a monster should use a special ability."""
        # Basic logic for common abilities
        if ability_name.lower() in ["nimble escape", "disengage"]:
            # Use if low on health
            return monster.hit_points.current <= monster.hit_points.maximum * 0.3
            
        if ability_name.lower() in ["aggressive", "charge"]:
            # Use if not adjacent to enemies (simplified)
            return True
            
        # Default: use special abilities randomly 30% of the time
        return random.random() < 0.3
        
    def should_heal(self, character: Character) -> bool:
        """Determine if a character should prioritize healing."""
        hp_percentage = character.hit_points.current / character.hit_points.maximum
        
        # Heal if below 25% health
        return hp_percentage < 0.25
        
    def should_retreat(self, character: Character) -> bool:
        """Determine if a character should retreat or take defensive actions."""
        hp_percentage = character.hit_points.current / character.hit_points.maximum
        
        # Consider retreating if below 15% health
        return hp_percentage < 0.15

class AutoCombat:
    """Manages fully automated combat simulation."""
    
    def __init__(self, combat_engine: CombatEngine):
        self.combat_engine = combat_engine
        self.ai = CombatAI(combat_engine)
        self.max_rounds = 100  # Prevent infinite combat
        
    def run_full_simulation(self) -> bool:
        """
        Run a complete automated combat simulation.
        
        Returns:
            True if combat completed normally, False if stopped due to max rounds
        """
        if not self.combat_engine.participants:
            return False
            
        self.combat_engine.start_combat()
        
        while (self.combat_engine.state.value == "active" and 
               self.combat_engine.current_round <= self.max_rounds):
            
            current_actor = self.combat_engine.get_current_actor()
            if not current_actor:
                break
                
            if current_actor.can_take_actions():
                # Get AI decision
                action_type, target, attack = self.ai.choose_action(current_actor)
                
                # Execute the action
                if action_type == ActionType.ATTACK and target and attack:
                    self.combat_engine.make_attack(current_actor, target, attack)
                elif action_type == ActionType.DODGE:
                    self.combat_engine.log_message(f"{current_actor.name} takes the Dodge action")
                else:
                    self.combat_engine.log_message(f"{current_actor.name} takes no action")
                    
            # Advance to next turn
            self.combat_engine.next_turn()
            
        # Check if we hit max rounds
        if self.combat_engine.current_round > self.max_rounds:
            self.combat_engine.log_message(f"Combat ended due to round limit ({self.max_rounds})")
            self.combat_engine.end_combat()
            return False
            
        return True
        
    def run_single_turn(self) -> bool:
        """
        Run a single turn of automated combat.
        
        Returns:
            True if combat is still active, False if ended
        """
        if self.combat_engine.state.value != "active":
            return False
            
        current_actor = self.combat_engine.get_current_actor()
        if not current_actor:
            return False
            
        if current_actor.can_take_actions():
            # Get AI decision
            action_type, target, attack = self.ai.choose_action(current_actor)
            
            # Execute the action
            if action_type == ActionType.ATTACK and target and attack:
                self.combat_engine.make_attack(current_actor, target, attack)
            elif action_type == ActionType.DODGE:
                self.combat_engine.log_message(f"{current_actor.name} takes the Dodge action")
            else:
                self.combat_engine.log_message(f"{current_actor.name} takes no action")
                
        # Advance to next turn
        self.combat_engine.next_turn()
        
        return self.combat_engine.state.value == "active"
