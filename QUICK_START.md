# D&D Combat Simulator - Quick Start Guide

## 🚀 Getting Started

### Launch the Application
```bash
python main.py
```

### First Time Setup
1. **Add Characters**: Click "Add Sample" to quickly add pre-made characters, or "Add Character" to create custom ones
2. **Add Monsters**: Click "Add Monster" to browse and select monsters for your encounter
3. **Choose Mode**: In the Settings tab, select your preferred simulation mode
4. **Start Combat**: Click "Start Combat" to begin the simulation

## 🎮 Using the Interface

### Characters Tab
- **Add Character**: Create a custom character with full D&D stats
- **Add Sample**: Quick-add pre-made Level 3 characters (<PERSON>, <PERSON>, <PERSON>, Cleric)
- **Edit Character**: Modify existing character stats
- **Remove Character**: Delete characters from the party

### Monsters Tab
- **Add Monster**: Browse SRD monsters by Challenge Rating
- **Filter by CR**: Use Min/Max CR filters to find appropriate monsters
- **Quantity**: Add multiple copies of the same monster
- **Monster Details**: View full stat blocks before adding

### Settings Tab
- **Manual Control**: You control all actions (not fully implemented yet)
- **Auto Simulation**: AI controls everything, runs to completion
- **Step-by-Step**: AI makes decisions, advance one turn at a time

### Combat Controls
- **Start Combat**: Begin the encounter with current participants
- **Pause**: Stop auto-simulation (resume with Start)
- **Reset**: Clear combat and restore all HP
- **Next Turn**: Advance one turn in step-by-step mode

## 🎯 Recommended Workflows

### For Players - Combat Practice
1. Add your character (or use a sample)
2. Add 1-2 monsters of appropriate CR
3. Use "Step-by-Step" mode to see each action
4. Observe how different tactics affect outcomes

### For DMs - Encounter Testing
1. Add your full party (4 characters recommended)
2. Try different monster combinations
3. Use "Auto Simulation" for quick results
4. Compare outcomes with different CR levels

### For Both - Learning D&D Mechanics
1. Start with sample characters vs single monsters
2. Watch the combat log to understand:
   - Initiative order and turn sequence
   - Attack rolls and damage calculation
   - How AC affects hit chances
   - Status effects and conditions

## 📊 Understanding the Results

### Combat Log
- Shows detailed turn-by-turn actions
- Displays dice rolls and calculations
- Tracks damage, healing, and status changes
- Records initiative order and round progression

### HP Tracking
- Real-time HP updates in character/monster lists
- Shows current/maximum HP
- Indicates unconscious and dead states
- Tracks temporary HP and healing

### Combat Duration
- Round counter shows encounter length
- Helps assess encounter difficulty
- Longer combats may indicate balanced encounters
- Very short combats suggest imbalance

## 🔧 Tips and Tricks

### Character Creation
- Use standard D&D ability score arrays (15,14,13,12,10,8)
- Remember to set appropriate AC for armor type
- Don't forget saving throw proficiencies by class
- HP should reflect class hit die + CON modifier per level

### Monster Selection
- CR 1/4 per character is typically "Easy"
- CR 1/2 per character is "Medium" 
- CR 1 per character is "Hard"
- Multiple weak monsters vs few strong ones play differently

### Encounter Design
- Action economy matters: 4 goblins vs 1 ogre feels very different
- Consider monster special abilities and tactics
- Test the same party against various encounter types
- Use the simulator to validate your encounter math

### Interpreting AI Behavior
- AI prioritizes low HP targets (realistic tactic)
- Monsters use their best available attacks
- Characters without defined attacks struggle (limitation)
- AI doesn't use complex tactics or positioning

## 🐛 Known Limitations & Workarounds

### Characters Don't Have Attacks
**Issue**: Sample characters lack weapon/spell attacks  
**Workaround**: Focus on testing monster encounters, or add attack definitions to character class

### AI is Basic
**Issue**: Simple target selection and attack choices  
**Workaround**: Use results as baseline, apply your own tactical knowledge

### No Spell System
**Issue**: Spellcasters can't cast spells yet  
**Workaround**: Treat as baseline melee combat, plan spell system addition

### No Positioning
**Issue**: No movement or range tracking  
**Workaround**: Assume optimal positioning for testing purposes

## 📁 File Management

### Saving Characters
- Use File → Save Character to export
- Characters saved as JSON files
- Can be shared between users
- Load with File → Load Character

### Sample Data
- Pre-made characters in `src/data/sample_characters.py`
- Monster database in `src/data/monsters.py`
- Easy to add new monsters or characters by editing these files

## 🔮 Next Steps

### Immediate Improvements You Can Make
1. **Add Character Attacks**: Edit sample characters to include weapon attacks
2. **More Monsters**: Add additional SRD monsters to the database
3. **Custom Encounters**: Create specific scenarios for your campaign

### Advanced Usage
1. **Batch Testing**: Run multiple simulations to get statistical averages
2. **Party Optimization**: Test different character builds and compositions
3. **Monster Tactics**: Observe AI behavior to inspire your own monster tactics

## 🆘 Troubleshooting

### Application Won't Start
- Ensure Python 3.8+ is installed
- Check that all files are in the correct directory structure
- Try running `python test_combat.py` to verify core functionality

### Import Errors
- Make sure you're running from the DNDSim directory
- Verify all src/ subdirectories and files are present
- Check that __init__.py files exist in src/ folders

### Combat Seems Unbalanced
- This is actually useful data! The simulator reveals true encounter difficulty
- Consider that D&D encounters often favor the side with more actions per round
- Remember that player tactics and spells can dramatically change outcomes

---

**Have fun exploring D&D combat mechanics!** 🎲

The simulator is a powerful tool for understanding how the math behind D&D actually plays out in practice.
