"""
Monster selection dialog for D&D Combat Simulator
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, List

from ..core.monster import Monster
from ..data.monsters import get_available_monsters, create_monster, get_monsters_by_cr

class MonsterDialog:
    """Dialog for selecting and adding monsters to combat."""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Monster")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (600 // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.create_widgets()
        self.load_monsters()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def create_widgets(self):
        """Create the dialog widgets."""
        # Main frame
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Filter frame
        filter_frame = ttk.LabelFrame(main_frame, text="Filters")
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # CR filter
        ttk.Label(filter_frame, text="Challenge Rating:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="Min:").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.min_cr_var = tk.StringVar(value="0")
        min_cr_combo = ttk.Combobox(filter_frame, textvariable=self.min_cr_var, width=8)
        min_cr_combo['values'] = ["0", "0.125", "0.25", "0.5"] + [str(i) for i in range(1, 31)]
        min_cr_combo.grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="Max:").grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        self.max_cr_var = tk.StringVar(value="30")
        max_cr_combo = ttk.Combobox(filter_frame, textvariable=self.max_cr_var, width=8)
        max_cr_combo['values'] = ["0", "0.125", "0.25", "0.5"] + [str(i) for i in range(1, 31)]
        max_cr_combo.grid(row=0, column=4, padx=5, pady=5)
        
        ttk.Button(filter_frame, text="Apply Filter", command=self.apply_filter).grid(row=0, column=5, padx=10, pady=5)
        
        # Monster list frame
        list_frame = ttk.LabelFrame(main_frame, text="Available Monsters")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Create treeview for monster list
        columns = ("Name", "CR", "Type", "HP", "AC")
        self.monster_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # Configure columns
        self.monster_tree.heading("Name", text="Name")
        self.monster_tree.heading("CR", text="CR")
        self.monster_tree.heading("Type", text="Type")
        self.monster_tree.heading("HP", text="HP")
        self.monster_tree.heading("AC", text="AC")
        
        self.monster_tree.column("Name", width=150)
        self.monster_tree.column("CR", width=50)
        self.monster_tree.column("Type", width=100)
        self.monster_tree.column("HP", width=50)
        self.monster_tree.column("AC", width=50)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.monster_tree.yview)
        self.monster_tree.configure(yscrollcommand=scrollbar.set)
        
        self.monster_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click to select
        self.monster_tree.bind("<Double-1>", lambda e: self.ok())
        
        # Quantity frame
        qty_frame = ttk.LabelFrame(main_frame, text="Quantity")
        qty_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(qty_frame, text="Number of monsters:").pack(side=tk.LEFT, padx=5, pady=5)
        self.quantity_var = tk.IntVar(value=1)
        ttk.Spinbox(qty_frame, from_=1, to=20, textvariable=self.quantity_var, width=10).pack(side=tk.LEFT, padx=5, pady=5)
        
        # Monster details frame
        details_frame = ttk.LabelFrame(main_frame, text="Monster Details")
        details_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.details_text = tk.Text(details_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        details_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Bind selection change to show details
        self.monster_tree.bind("<<TreeviewSelect>>", self.on_monster_select)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="OK", command=self.ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT, padx=5)
        
    def load_monsters(self):
        """Load monsters into the treeview."""
        # Clear existing items
        for item in self.monster_tree.get_children():
            self.monster_tree.delete(item)
            
        # Get filtered monsters
        try:
            min_cr = float(self.min_cr_var.get())
            max_cr = float(self.max_cr_var.get())
        except ValueError:
            min_cr, max_cr = 0, 30
            
        monster_names = get_monsters_by_cr(min_cr, max_cr)
        
        # Add monsters to treeview
        for name in sorted(monster_names):
            try:
                monster = create_monster(name)
                self.monster_tree.insert("", tk.END, values=(
                    monster.name,
                    monster.challenge_rating,
                    monster.monster_type.value.title(),
                    monster.hit_points.maximum,
                    monster.armor_class.total
                ))
            except Exception as e:
                print(f"Error loading monster {name}: {e}")
                
    def apply_filter(self):
        """Apply CR filter to monster list."""
        self.load_monsters()
        
    def on_monster_select(self, event):
        """Handle monster selection to show details."""
        selection = self.monster_tree.selection()
        if not selection:
            return
            
        item = self.monster_tree.item(selection[0])
        monster_name = item['values'][0]
        
        try:
            monster = create_monster(monster_name)
            self.show_monster_details(monster)
        except Exception as e:
            self.show_error_details(f"Error loading monster details: {e}")
            
    def show_monster_details(self, monster: Monster):
        """Display monster details in the text widget."""
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        
        details = f"Name: {monster.name}\n"
        details += f"Challenge Rating: {monster.challenge_rating} (XP: {monster.calculate_xp_value()})\n"
        details += f"Type: {monster.monster_type.value.title()}, Size: {monster.size.value.title()}\n"
        details += f"Alignment: {monster.alignment}\n\n"
        
        details += f"Armor Class: {monster.armor_class.total}\n"
        details += f"Hit Points: {monster.hit_points.maximum}\n"
        details += f"Speed: {monster.speed} ft.\n\n"
        
        # Ability scores
        details += "Ability Scores:\n"
        abilities = ["STR", "DEX", "CON", "INT", "WIS", "CHA"]
        scores = [
            monster.ability_scores.strength,
            monster.ability_scores.dexterity,
            monster.ability_scores.constitution,
            monster.ability_scores.intelligence,
            monster.ability_scores.wisdom,
            monster.ability_scores.charisma
        ]
        
        for ability, score in zip(abilities, scores):
            modifier = (score - 10) // 2
            mod_str = f"+{modifier}" if modifier >= 0 else str(modifier)
            details += f"{ability}: {score} ({mod_str})  "
        details += "\n\n"
        
        # Attacks
        if monster.attacks:
            details += "Attacks:\n"
            for attack in monster.attacks:
                details += f"• {attack.name}: +{attack.attack_bonus} to hit, "
                details += f"{attack.damage_dice}+{attack.damage_bonus} {attack.damage_type} damage\n"
            details += "\n"
            
        # Special abilities
        if monster.special_abilities:
            details += "Special Abilities:\n"
            for ability in monster.special_abilities:
                details += f"• {ability.name}: {ability.description}\n"
            details += "\n"
            
        # Damage resistances/immunities
        if monster.damage_resistances:
            details += f"Damage Resistances: {', '.join(monster.damage_resistances)}\n"
        if monster.damage_immunities:
            details += f"Damage Immunities: {', '.join(monster.damage_immunities)}\n"
        if monster.damage_vulnerabilities:
            details += f"Damage Vulnerabilities: {', '.join(monster.damage_vulnerabilities)}\n"
            
        self.details_text.insert(1.0, details)
        self.details_text.config(state=tk.DISABLED)
        
    def show_error_details(self, error_message: str):
        """Display error message in details area."""
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, error_message)
        self.details_text.config(state=tk.DISABLED)
        
    def get_selected_monster_name(self) -> Optional[str]:
        """Get the name of the selected monster."""
        selection = self.monster_tree.selection()
        if not selection:
            return None
            
        item = self.monster_tree.item(selection[0])
        return item['values'][0]
        
    def ok(self):
        """Handle OK button click."""
        monster_name = self.get_selected_monster_name()
        if not monster_name:
            messagebox.showwarning("No Selection", "Please select a monster.")
            return
            
        try:
            # Create the requested number of monsters
            monsters = []
            quantity = self.quantity_var.get()
            
            for i in range(quantity):
                monster = create_monster(monster_name)
                if quantity > 1:
                    monster.name = f"{monster.name} {i+1}"
                monsters.append(monster)
                
            self.result = monsters
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create monster: {str(e)}")
            
    def cancel(self):
        """Handle Cancel button click."""
        self.result = None
        self.dialog.destroy()
        
    def show(self) -> Optional[List[Monster]]:
        """Show the dialog and return the result."""
        self.dialog.wait_window()
        return self.result
