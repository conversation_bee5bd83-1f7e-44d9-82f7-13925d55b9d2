"""
Character creation and editing dialog for D&D Combat Simulator
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional

from src.core.character import Character, CharacterClass, Size, HitPoints, ArmorClass
from src.core.abilities import AbilityScores, AbilityType, SavingThrows
from src.core.weapons import get_all_weapon_names, get_weapon, WeaponCategory
from src.core.attacks import create_weapon_attack, create_spell_attack, create_unarmed_strike
from src.core.weapons import DamageType
from src.core.class_features import get_class_info, get_starting_attacks, get_available_cantrips, get_weapon_proficiencies

class CharacterDialog:
    """Dialog for creating and editing characters."""
    
    def __init__(self, parent, character: Optional[Character] = None):
        self.parent = parent
        self.character = character
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Character Editor" if character else "Create Character")
        self.dialog.geometry("650x800")
        self.dialog.resizable(True, True)

        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (650 // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (800 // 2)
        self.dialog.geometry(f"650x800+{x}+{y}")
        
        self.create_widgets()
        self.load_character_data()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def create_widgets(self):
        """Create the dialog widgets."""
        # Create main container
        container = ttk.Frame(self.dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(container)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Main frame (now inside scrollable frame)
        main_frame = ttk.Frame(self.scrollable_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Bind mousewheel to canvas for scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        # Bind/unbind mousewheel when entering/leaving the canvas
        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        # Store canvas reference for cleanup
        self.canvas = canvas
        
        # Basic Information
        basic_frame = ttk.LabelFrame(main_frame, text="Basic Information")
        basic_frame.pack(fill=tk.X, pady=5)
        
        # Name
        ttk.Label(basic_frame, text="Name:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, padx=5, pady=2)
        
        # Level
        ttk.Label(basic_frame, text="Level:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.level_var = tk.IntVar(value=1)
        level_spinbox = ttk.Spinbox(basic_frame, from_=1, to=20, textvariable=self.level_var, width=10)
        level_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Class
        ttk.Label(basic_frame, text="Class:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.class_var = tk.StringVar()
        class_combo = ttk.Combobox(basic_frame, textvariable=self.class_var, width=27)
        class_combo['values'] = [cls.value.title() for cls in CharacterClass]
        class_combo.grid(row=2, column=1, padx=5, pady=2)
        class_combo.state(['readonly'])
        class_combo.bind('<<ComboboxSelected>>', self.on_class_changed)
        
        # Size
        ttk.Label(basic_frame, text="Size:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.size_var = tk.StringVar()
        size_combo = ttk.Combobox(basic_frame, textvariable=self.size_var, width=27)
        size_combo['values'] = [size.value.title() for size in Size]
        size_combo.grid(row=3, column=1, padx=5, pady=2)
        size_combo.state(['readonly'])

        # Race/Species
        ttk.Label(basic_frame, text="Race/Species:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.race_var = tk.StringVar()
        race_combo = ttk.Combobox(basic_frame, textvariable=self.race_var, width=27)
        race_combo['values'] = ["Human", "Elf", "Dwarf", "Halfling", "Dragonborn", "Gnome", "Half-Elf", "Half-Orc", "Tiefling"]
        race_combo.grid(row=4, column=1, padx=5, pady=2)
        race_combo.state(['readonly'])

        # Background
        ttk.Label(basic_frame, text="Background:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        self.background_var = tk.StringVar()
        background_combo = ttk.Combobox(basic_frame, textvariable=self.background_var, width=27)
        background_combo['values'] = ["Acolyte", "Criminal", "Folk Hero", "Noble", "Sage", "Soldier", "Charlatan", "Entertainer", "Guild Artisan", "Hermit", "Outlander", "Sailor"]
        background_combo.grid(row=5, column=1, padx=5, pady=2)
        background_combo.state(['readonly'])
        
        # Ability Scores
        abilities_frame = ttk.LabelFrame(main_frame, text="Ability Scores")
        abilities_frame.pack(fill=tk.X, pady=5)
        
        self.ability_vars = {}
        abilities = ["Strength", "Dexterity", "Constitution", "Intelligence", "Wisdom", "Charisma"]
        
        for i, ability in enumerate(abilities):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(abilities_frame, text=f"{ability}:").grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
            var = tk.IntVar(value=10)
            self.ability_vars[ability.lower()] = var
            spinbox = ttk.Spinbox(abilities_frame, from_=1, to=30, textvariable=var, width=10)
            spinbox.grid(row=row, column=col+1, padx=5, pady=2)
        
        # Hit Points
        hp_frame = ttk.LabelFrame(main_frame, text="Hit Points")
        hp_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(hp_frame, text="Maximum HP:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_hp_var = tk.IntVar(value=10)
        ttk.Spinbox(hp_frame, from_=1, to=999, textvariable=self.max_hp_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(hp_frame, text="Current HP:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.current_hp_var = tk.IntVar(value=10)
        ttk.Spinbox(hp_frame, from_=0, to=999, textvariable=self.current_hp_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        # Armor Class
        ac_frame = ttk.LabelFrame(main_frame, text="Armor Class")
        ac_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(ac_frame, text="Base AC:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.base_ac_var = tk.IntVar(value=10)
        ttk.Spinbox(ac_frame, from_=1, to=30, textvariable=self.base_ac_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(ac_frame, text="Armor Bonus:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.armor_bonus_var = tk.IntVar(value=0)
        ttk.Spinbox(ac_frame, from_=0, to=20, textvariable=self.armor_bonus_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        ttk.Label(ac_frame, text="Shield Bonus:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.shield_bonus_var = tk.IntVar(value=0)
        ttk.Spinbox(ac_frame, from_=0, to=10, textvariable=self.shield_bonus_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Label(ac_frame, text="Misc Bonus:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.misc_ac_var = tk.IntVar(value=0)
        ttk.Spinbox(ac_frame, from_=-10, to=10, textvariable=self.misc_ac_var, width=10).grid(row=1, column=3, padx=5, pady=2)
        
        # Other Stats
        other_frame = ttk.LabelFrame(main_frame, text="Other Stats")
        other_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(other_frame, text="Speed:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.speed_var = tk.IntVar(value=30)
        ttk.Spinbox(other_frame, from_=0, to=120, textvariable=self.speed_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        # Saving Throw Proficiencies
        saves_frame = ttk.LabelFrame(main_frame, text="Saving Throw Proficiencies")
        saves_frame.pack(fill=tk.X, pady=5)

        self.save_vars = {}
        for i, ability in enumerate(abilities):
            var = tk.BooleanVar()
            self.save_vars[ability.lower()] = var
            ttk.Checkbutton(saves_frame, text=ability, variable=var).grid(row=i//3, column=i%3, sticky=tk.W, padx=5, pady=2)

        # Attacks section
        attacks_frame = ttk.LabelFrame(main_frame, text="Attacks & Spells")
        attacks_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Class-based attacks info
        class_info_frame = ttk.Frame(attacks_frame)
        class_info_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(class_info_frame, text="Class attacks will be automatically added based on class selection.",
                 font=("TkDefaultFont", 8)).pack(anchor=tk.W)

        # Attack list
        attack_list_frame = ttk.Frame(attacks_frame)
        attack_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.attack_listbox = tk.Listbox(attack_list_frame, height=6)
        attack_scrollbar = ttk.Scrollbar(attack_list_frame, orient=tk.VERTICAL, command=self.attack_listbox.yview)
        self.attack_listbox.config(yscrollcommand=attack_scrollbar.set)

        self.attack_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        attack_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Attack buttons
        attack_btn_frame = ttk.Frame(attacks_frame)
        attack_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(attack_btn_frame, text="Add Class Attacks", command=self.add_class_attacks).pack(side=tk.LEFT, padx=2)
        ttk.Button(attack_btn_frame, text="Add Weapon", command=self.add_weapon_attack).pack(side=tk.LEFT, padx=2)
        ttk.Button(attack_btn_frame, text="Add Cantrip", command=self.add_cantrip_attack).pack(side=tk.LEFT, padx=2)
        ttk.Button(attack_btn_frame, text="Remove", command=self.remove_attack).pack(side=tk.LEFT, padx=2)
        
        # Buttons (outside scrollable area)
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(button_frame, text="OK", command=self.ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT, padx=5)
        
        # Auto-calculate AC when dexterity changes
        self.ability_vars['dexterity'].trace('w', self.update_ac_preview)
        
    def update_ac_preview(self, *args):
        """Update AC preview when dexterity changes."""
        # This could show a calculated AC preview
        pass
        
    def load_character_data(self):
        """Load existing character data into the form."""
        if not self.character:
            # Set defaults for new character
            self.class_var.set("Fighter")
            self.size_var.set("Medium")
            self.race_var.set("Human")
            self.background_var.set("Soldier")
            self.current_attacks = []
            return
            
        # Load existing character data
        self.name_var.set(self.character.name)
        self.level_var.set(self.character.level)
        self.class_var.set(self.character.character_class.value.title())
        self.size_var.set(self.character.size.value.title())
        
        # Ability scores
        for ability_name, var in self.ability_vars.items():
            ability_type = AbilityType(ability_name)
            score = self.character.ability_scores.get_score(ability_type)
            var.set(score)
            
        # Hit points
        self.max_hp_var.set(self.character.hit_points.maximum)
        self.current_hp_var.set(self.character.hit_points.current)
        
        # Armor class
        self.base_ac_var.set(self.character.armor_class.base)
        self.armor_bonus_var.set(self.character.armor_class.armor_bonus)
        self.shield_bonus_var.set(self.character.armor_class.shield_bonus)
        self.misc_ac_var.set(self.character.armor_class.misc_bonus)
        
        # Other stats
        self.speed_var.set(self.character.speed)
        
        # Saving throws
        for ability_name, var in self.save_vars.items():
            ability_type = AbilityType(ability_name)
            is_proficient = self.character.saving_throws.is_proficient(ability_type)
            var.set(is_proficient)

        # Load race and background
        if hasattr(self.character, 'race'):
            self.race_var.set(self.character.race)
        if hasattr(self.character, 'background'):
            self.background_var.set(self.character.background)

        # Load attacks
        if hasattr(self.character, 'attacks'):
            self.current_attacks = self.character.attacks.copy()
            self.update_attack_list()
            
    def validate_input(self) -> bool:
        """Validate the form input."""
        if not self.name_var.get().strip():
            messagebox.showerror("Error", "Character name is required.")
            return False
            
        if not self.class_var.get():
            messagebox.showerror("Error", "Character class is required.")
            return False
            
        if not self.size_var.get():
            messagebox.showerror("Error", "Character size is required.")
            return False
            
        return True
        
    def create_character(self) -> Character:
        """Create a character from the form data."""
        # Create character
        char_class = CharacterClass(self.class_var.get().lower())
        character = Character(self.name_var.get().strip(), self.level_var.get(), char_class)
        
        # Set size
        character.size = Size(self.size_var.get().lower())
        
        # Set ability scores
        character.ability_scores = AbilityScores(
            strength=self.ability_vars['strength'].get(),
            dexterity=self.ability_vars['dexterity'].get(),
            constitution=self.ability_vars['constitution'].get(),
            intelligence=self.ability_vars['intelligence'].get(),
            wisdom=self.ability_vars['wisdom'].get(),
            charisma=self.ability_vars['charisma'].get()
        )
        
        # Set hit points
        character.hit_points = HitPoints(
            maximum=self.max_hp_var.get(),
            current=self.current_hp_var.get()
        )
        
        # Set armor class
        dex_mod = character.get_ability_modifier(AbilityType.DEXTERITY)
        character.armor_class = ArmorClass(
            base=self.base_ac_var.get(),
            armor_bonus=self.armor_bonus_var.get(),
            shield_bonus=self.shield_bonus_var.get(),
            dex_modifier=dex_mod,
            misc_bonus=self.misc_ac_var.get()
        )
        
        # Set speed
        character.speed = self.speed_var.get()
        
        # Set saving throw proficiencies
        proficiencies = {}
        for ability_name, var in self.save_vars.items():
            if var.get():
                ability_type = AbilityType(ability_name)
                proficiencies[ability_type] = True
        character.saving_throws = SavingThrows(proficiencies)

        # Add attacks (stored in self.current_attacks during editing)
        if hasattr(self, 'current_attacks'):
            for attack in self.current_attacks:
                character.add_attack(attack)

        # Store race and background (for future use)
        character.race = self.race_var.get()
        character.background = self.background_var.get()

        return character

    def add_class_attacks(self):
        """Add class-appropriate attacks based on selected class."""
        try:
            class_name = self.class_var.get()
            character_class = CharacterClass(class_name.lower())
            level = self.level_var.get()

            # Get starting attacks for this class
            class_attacks = get_starting_attacks(character_class, level)

            if not hasattr(self, 'current_attacks'):
                self.current_attacks = []

            # Add attacks that aren't already present
            existing_names = {attack.name for attack in self.current_attacks}
            new_attacks = [attack for attack in class_attacks if attack.name not in existing_names]

            self.current_attacks.extend(new_attacks)
            self.update_attack_list()

            if new_attacks:
                messagebox.showinfo("Success", f"Added {len(new_attacks)} class-appropriate attacks.")
            else:
                messagebox.showinfo("Info", "All class attacks are already present.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to add class attacks: {str(e)}")

    def add_weapon_attack(self):
        """Add a weapon attack to the character."""
        # Create weapon selection dialog
        weapon_dialog = tk.Toplevel(self.dialog)
        weapon_dialog.title("Select Weapon")
        weapon_dialog.geometry("300x400")
        weapon_dialog.transient(self.dialog)
        weapon_dialog.grab_set()

        ttk.Label(weapon_dialog, text="Choose a weapon:").pack(pady=10)

        # Get class weapon proficiencies
        try:
            class_name = self.class_var.get()
            character_class = CharacterClass(class_name.lower())
            proficient_categories = get_weapon_proficiencies(character_class)
        except:
            proficient_categories = []

        # Weapon listbox
        weapon_listbox = tk.Listbox(weapon_dialog)
        weapon_names = get_all_weapon_names()

        # Filter weapons by proficiency if available
        if proficient_categories:
            filtered_weapons = []
            for name in weapon_names:
                weapon_data = get_weapon(name)
                if weapon_data and weapon_data.category in proficient_categories:
                    filtered_weapons.append(name)
            weapon_names = filtered_weapons

        for name in sorted(weapon_names):
            weapon_listbox.insert(tk.END, name)
        weapon_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        result = [None]

        def on_weapon_ok():
            selection = weapon_listbox.curselection()
            if selection:
                weapon_name = weapon_names[selection[0]]
                weapon_data = get_weapon(weapon_name)
                if weapon_data:
                    attack = create_weapon_attack(weapon_data)
                    if not hasattr(self, 'current_attacks'):
                        self.current_attacks = []
                    self.current_attacks.append(attack)
                    self.update_attack_list()
            weapon_dialog.destroy()

        def on_weapon_cancel():
            weapon_dialog.destroy()

        btn_frame = ttk.Frame(weapon_dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        ttk.Button(btn_frame, text="OK", command=on_weapon_ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="Cancel", command=on_weapon_cancel).pack(side=tk.RIGHT, padx=5)

        weapon_listbox.bind("<Double-1>", lambda e: on_weapon_ok())

    def add_cantrip_attack(self):
        """Add a cantrip attack based on class."""
        try:
            class_name = self.class_var.get()
            character_class = CharacterClass(class_name.lower())
            available_cantrips = get_available_cantrips(character_class)

            if not available_cantrips:
                messagebox.showinfo("No Cantrips", f"{class_name} doesn't have access to cantrips.")
                return

        except:
            messagebox.showerror("Error", "Please select a valid class first.")
            return

        # Cantrip selection dialog
        cantrip_dialog = tk.Toplevel(self.dialog)
        cantrip_dialog.title("Select Cantrip")
        cantrip_dialog.geometry("300x400")
        cantrip_dialog.transient(self.dialog)
        cantrip_dialog.grab_set()

        ttk.Label(cantrip_dialog, text="Choose a cantrip:").pack(pady=10)

        # Cantrip listbox
        cantrip_listbox = tk.Listbox(cantrip_dialog)
        cantrip_data = []

        for name, damage, damage_type, is_attack, range_ft in available_cantrips:
            if damage != "0":  # Only show damage cantrips
                display_text = f"{name} ({damage} {damage_type.value})"
                cantrip_listbox.insert(tk.END, display_text)
                cantrip_data.append((name, damage, damage_type, is_attack, range_ft))

        cantrip_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        def on_cantrip_ok():
            selection = cantrip_listbox.curselection()
            if selection:
                name, damage, damage_type, is_attack, range_ft = cantrip_data[selection[0]]
                attack = create_spell_attack(
                    name, damage, damage_type,
                    spell_attack=is_attack, range_normal=range_ft
                )
                if not hasattr(self, 'current_attacks'):
                    self.current_attacks = []
                self.current_attacks.append(attack)
                self.update_attack_list()
            cantrip_dialog.destroy()

        def on_cantrip_cancel():
            cantrip_dialog.destroy()

        btn_frame = ttk.Frame(cantrip_dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        ttk.Button(btn_frame, text="OK", command=on_cantrip_ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="Cancel", command=on_cantrip_cancel).pack(side=tk.RIGHT, padx=5)

        cantrip_listbox.bind("<Double-1>", lambda e: on_cantrip_ok())

    def remove_attack(self):
        """Remove the selected attack."""
        selection = self.attack_listbox.curselection()
        if selection and hasattr(self, 'current_attacks'):
            index = selection[0]
            if 0 <= index < len(self.current_attacks):
                self.current_attacks.pop(index)
                self.update_attack_list()
        else:
            messagebox.showwarning("No Selection", "Please select an attack to remove.")

    def update_attack_list(self):
        """Update the attack listbox display."""
        self.attack_listbox.delete(0, tk.END)
        if hasattr(self, 'current_attacks'):
            for attack in self.current_attacks:
                display_text = f"{attack.name} ({attack.damage_dice} {attack.damage_type.value})"
                self.attack_listbox.insert(tk.END, display_text)

    def on_class_changed(self, event=None):
        """Handle class selection change."""
        # Update saving throw proficiencies based on class
        try:
            class_name = self.class_var.get()
            character_class = CharacterClass(class_name.lower())
            class_info = get_class_info(character_class)

            if class_info:
                # Reset all saving throws
                for var in self.save_vars.values():
                    var.set(False)

                # Set class proficiencies
                for ability in class_info.saving_throw_proficiencies:
                    if ability.value in self.save_vars:
                        self.save_vars[ability.value].set(True)
        except:
            pass  # Ignore errors during class change
        
    def ok(self):
        """Handle OK button click."""
        if not self.validate_input():
            return
            
        try:
            self.result = self.create_character()
            # Clean up mousewheel binding
            if hasattr(self, 'canvas'):
                self.canvas.unbind_all("<MouseWheel>")
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create character: {str(e)}")
            
    def cancel(self):
        """Handle Cancel button click."""
        # Clean up mousewheel binding
        if hasattr(self, 'canvas'):
            self.canvas.unbind_all("<MouseWheel>")
        self.result = None
        self.dialog.destroy()
        
    def show(self) -> Optional[Character]:
        """Show the dialog and return the result."""
        self.dialog.wait_window()
        return self.result
