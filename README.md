# D&D Combat Simulator

A turn-based combat simulator for Dungeons & Dragons 2024 rules, designed for both players and Dungeon Masters.

## Features

### 🎯 Core Functionality
- **Turn-based Combat**: Full implementation of D&D 2024 combat rules
- **Character Management**: Create, edit, and save custom characters
- **Monster Database**: Pre-loaded SRD monsters with full stat blocks
- **Multiple Simulation Modes**: Manual control, auto-simulation, and step-by-step

### ⚔️ Combat System
- Initiative tracking and turn order
- Action economy (action, bonus action, reaction, movement)
- Attack rolls with advantage/disadvantage
- Damage calculation and hit point tracking
- Status effects and conditions
- Death saving throws
- Armor class and saving throw calculations

### 🧑‍🤝‍🧑 User Modes
- **Players**: Practice combat scenarios and learn spell/ability usage
- **DMs**: Test encounter balance and difficulty scaling
- **Both**: Simulate party vs monster encounters

### 🤖 AI System
- Basic combat AI for automated decisions
- Prioritizes tactical choices (low HP targets, optimal attacks)
- Supports full auto-simulation or step-by-step control

## Getting Started

### Prerequisites
- Python 3.8 or higher
- No additional dependencies required (uses built-in Tkinter)

### Installation
1. Download or clone this repository
2. Navigate to the DNDSim directory
3. Run the application:
   ```bash
   python main.py
   ```

### Quick Start Guide

1. **Add Characters**:
   - Click "Add Character" in the Characters tab
   - Fill in character details (name, level, class, ability scores, etc.)
   - Save the character to your party

2. **Add Monsters**:
   - Click "Add Monster" in the Monsters tab
   - Browse available monsters by Challenge Rating
   - Select monsters and quantity for your encounter

3. **Configure Combat**:
   - Choose simulation mode in the Settings tab:
     - **Manual Control**: You control all actions
     - **Auto Simulation**: AI controls everything
     - **Step-by-Step**: Advance turn by turn

4. **Start Combat**:
   - Click "Start Combat" to begin
   - Watch the combat log for detailed action descriptions
   - Use "Pause" or "Reset" as needed

## Combat Modes

### Manual Control
- Full player control over all character and monster actions
- Best for learning combat mechanics
- Requires user input for each action

### Auto Simulation
- AI controls all participants
- Runs combat to completion automatically
- Great for testing encounter balance

### Step-by-Step
- AI makes decisions but advances one turn at a time
- Use "Next Turn" button to proceed
- Perfect for analyzing combat flow

## Character Creation

### Ability Scores
- Enter raw ability scores (3-18 typical range)
- Modifiers are calculated automatically
- Affects attack bonuses, saving throws, and AC

### Hit Points
- Set maximum and current HP
- Tracks damage and healing during combat
- Handles unconsciousness and death

### Armor Class
- Configure base AC, armor bonus, shield bonus
- Dexterity modifier applied automatically
- Supports miscellaneous bonuses

### Saving Throws
- Select proficient saving throws by class
- Proficiency bonus calculated by level
- Used for spell effects and conditions

## Monster Database

### Available Monsters
- **CR 1/4**: Goblin, Wolf, Skeleton
- **CR 1/2**: Orc
- **CR 2**: Ogre
- More monsters can be added to the database

### Monster Features
- Full stat blocks with attacks and special abilities
- Damage resistances, immunities, and vulnerabilities
- Challenge Rating and XP values
- Automatic AI behavior

## File Management

### Saving Characters
- Use File → Save Character to export character data
- Characters saved as JSON files
- Can be loaded in future sessions

### Loading Characters
- Use File → Load Character to import saved characters
- Supports JSON format character files
- Maintains all character data and settings

## Tips for Players

1. **Practice Spell Usage**: Create scenarios to test different spell combinations
2. **Learn Action Economy**: Understand when to use actions, bonus actions, and reactions
3. **Test Builds**: Try different character builds against various monsters
4. **Understand Positioning**: While not graphical, consider reach and range

## Tips for DMs

1. **Encounter Balance**: Test different monster combinations against your party
2. **CR Scaling**: See how Challenge Rating translates to actual difficulty
3. **Action Economy**: Understand how multiple weak monsters compare to single strong ones
4. **Player Capabilities**: Test how different party compositions handle encounters

## Technical Details

### Architecture
- **Core**: Game mechanics and data models
- **GUI**: Tkinter-based user interface
- **AI**: Basic combat decision system
- **Data**: Monster database and character storage

### File Structure
```
DNDSim/
├── main.py                 # Application entry point
├── src/
│   ├── core/              # Core game mechanics
│   ├── gui/               # User interface
│   ├── ai/                # Combat AI system
│   └── data/              # Monster and character data
└── README.md              # This file
```

## Known Limitations

- No graphical combat map (text-based positioning)
- Limited to SRD monsters (no copyrighted content)
- Basic AI (not advanced tactical decisions)
- No spell system implementation yet
- Windows desktop only (no web/mobile)

## Future Enhancements

- D&D Beyond character import
- Advanced spell casting system
- More sophisticated AI
- Additional monster database
- Combat map visualization
- Multi-player support

## Contributing

This is a personal project, but suggestions and feedback are welcome! The codebase is designed to be modular and extensible.

## License

This project uses only SRD (System Reference Document) content, which is available under the Open Game License. No copyrighted D&D content is included.

---

**Disclaimer**: This is an unofficial tool for D&D gameplay assistance. It is not affiliated with or endorsed by Wizards of the Coast.
