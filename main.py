#!/usr/bin/env python3
"""
D&D Combat Simulator - Main Entry Point
A turn-based combat simulator for D&D 2024 rules.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add the src directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.gui.main_window import MainWindow

def main():
    """Main entry point for the D&D Combat Simulator."""
    try:
        # Create the main window
        root = tk.Tk()
        app = MainWindow(root)
        
        # Start the GUI event loop
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
