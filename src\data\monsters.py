"""
Monster database with SRD monsters for D&D Combat Simulator
"""

from typing import Dict, List
from ..core.monster import Monster, Attack, SpecialAbility, MonsterType
from ..core.character import Size, HitPoints, ArmorClass
from ..core.abilities import AbilityScores, AbilityType, SavingThrows

def create_goblin() -> Monster:
    """Create a Goblin monster (CR 1/4)."""
    goblin = Monster("Goblin", challenge_rating=0.25)
    
    # Basic stats
    goblin.size = Size.SMALL
    goblin.monster_type = MonsterType.HUMANOID
    goblin.alignment = "neutral evil"
    
    # Ability scores
    goblin.ability_scores = AbilityScores(
        strength=8, dexterity=14, constitution=10,
        intelligence=10, wisdom=8, charisma=8
    )
    
    # Hit points and AC
    goblin.hit_points = HitPoints(maximum=7, current=7)
    goblin.armor_class = ArmorClass(
        base=10,
        armor_bonus=1,  # Leather armor
        dex_modifier=goblin.get_ability_modifier(AbilityType.DEXTERITY)
    )
    
    goblin.speed = 30
    goblin.passive_perception = 9
    goblin.languages = ["Common", "Goblin"]
    
    # Attacks
    scimitar = Attack(
        name="Scimitar",
        attack_bonus=4,
        damage_dice="1d6",
        damage_type="slashing",
        damage_bonus=2,
        reach=5
    )
    
    shortbow = Attack(
        name="Shortbow",
        attack_bonus=4,
        damage_dice="1d6",
        damage_type="piercing",
        damage_bonus=2,
        range_normal=80,
        range_long=320
    )
    
    goblin.add_attack(scimitar)
    goblin.add_attack(shortbow)
    
    # Special abilities
    nimble_escape = SpecialAbility(
        name="Nimble Escape",
        description="The goblin can take the Disengage or Hide action as a bonus action on each of its turns."
    )
    goblin.add_special_ability(nimble_escape)
    
    return goblin

def create_orc() -> Monster:
    """Create an Orc monster (CR 1/2)."""
    orc = Monster("Orc", challenge_rating=0.5)
    
    # Basic stats
    orc.size = Size.MEDIUM
    orc.monster_type = MonsterType.HUMANOID
    orc.alignment = "chaotic evil"
    
    # Ability scores
    orc.ability_scores = AbilityScores(
        strength=16, dexterity=12, constitution=16,
        intelligence=7, wisdom=11, charisma=10
    )
    
    # Hit points and AC
    orc.hit_points = HitPoints(maximum=15, current=15)
    orc.armor_class = ArmorClass(
        base=10,
        armor_bonus=3,  # Studded leather
        dex_modifier=orc.get_ability_modifier(AbilityType.DEXTERITY)
    )
    
    orc.speed = 30
    orc.darkvision = 60
    orc.passive_perception = 10
    orc.languages = ["Common", "Orc"]
    
    # Attacks
    greataxe = Attack(
        name="Greataxe",
        attack_bonus=5,
        damage_dice="1d12",
        damage_type="slashing",
        damage_bonus=3,
        reach=5
    )
    
    javelin = Attack(
        name="Javelin",
        attack_bonus=5,
        damage_dice="1d6",
        damage_type="piercing",
        damage_bonus=3,
        reach=5,
        range_normal=30,
        range_long=120
    )
    
    orc.add_attack(greataxe)
    orc.add_attack(javelin)
    
    # Special abilities
    aggressive = SpecialAbility(
        name="Aggressive",
        description="As a bonus action, the orc can move up to its speed toward a hostile creature that it can see."
    )
    orc.add_special_ability(aggressive)
    
    return orc

def create_wolf() -> Monster:
    """Create a Wolf monster (CR 1/4)."""
    wolf = Monster("Wolf", challenge_rating=0.25)
    
    # Basic stats
    wolf.size = Size.MEDIUM
    wolf.monster_type = MonsterType.BEAST
    wolf.alignment = "unaligned"
    
    # Ability scores
    wolf.ability_scores = AbilityScores(
        strength=12, dexterity=15, constitution=12,
        intelligence=3, wisdom=12, charisma=6
    )
    
    # Hit points and AC
    wolf.hit_points = HitPoints(maximum=11, current=11)
    wolf.armor_class = ArmorClass(
        base=10,
        dex_modifier=wolf.get_ability_modifier(AbilityType.DEXTERITY),
        natural_armor=3
    )
    
    wolf.speed = 40
    wolf.passive_perception = 13
    
    # Attacks
    bite = Attack(
        name="Bite",
        attack_bonus=4,
        damage_dice="2d4",
        damage_type="piercing",
        damage_bonus=2,
        reach=5,
        description="If the target is a creature, it must succeed on a DC 11 Strength saving throw or be knocked prone."
    )
    
    wolf.add_attack(bite)
    
    # Special abilities
    pack_tactics = SpecialAbility(
        name="Pack Tactics",
        description="The wolf has advantage on an attack roll against a creature if at least one of the wolf's allies is within 5 feet of the creature and the ally isn't incapacitated."
    )
    
    keen_hearing = SpecialAbility(
        name="Keen Hearing and Smell",
        description="The wolf has advantage on Wisdom (Perception) checks that rely on hearing or smell."
    )
    
    wolf.add_special_ability(pack_tactics)
    wolf.add_special_ability(keen_hearing)
    
    return wolf

def create_skeleton() -> Monster:
    """Create a Skeleton monster (CR 1/4)."""
    skeleton = Monster("Skeleton", challenge_rating=0.25)
    
    # Basic stats
    skeleton.size = Size.MEDIUM
    skeleton.monster_type = MonsterType.UNDEAD
    skeleton.alignment = "lawful evil"
    
    # Ability scores
    skeleton.ability_scores = AbilityScores(
        strength=10, dexterity=14, constitution=15,
        intelligence=6, wisdom=8, charisma=5
    )
    
    # Hit points and AC
    skeleton.hit_points = HitPoints(maximum=13, current=13)
    skeleton.armor_class = ArmorClass(
        base=10,
        armor_bonus=2,  # Armor scraps
        dex_modifier=skeleton.get_ability_modifier(AbilityType.DEXTERITY)
    )
    
    skeleton.speed = 30
    skeleton.darkvision = 60
    skeleton.passive_perception = 9
    skeleton.languages = ["understands all languages it knew in life but can't speak"]
    
    # Damage vulnerabilities, resistances, and immunities
    skeleton.damage_vulnerabilities = ["bludgeoning"]
    skeleton.damage_immunities = ["poison"]
    skeleton.condition_immunities = ["exhaustion", "poisoned"]
    
    # Attacks
    shortsword = Attack(
        name="Shortsword",
        attack_bonus=4,
        damage_dice="1d6",
        damage_type="piercing",
        damage_bonus=2,
        reach=5
    )
    
    shortbow = Attack(
        name="Shortbow",
        attack_bonus=4,
        damage_dice="1d6",
        damage_type="piercing",
        damage_bonus=2,
        range_normal=80,
        range_long=320
    )
    
    skeleton.add_attack(shortsword)
    skeleton.add_attack(shortbow)
    
    return skeleton

def create_ogre() -> Monster:
    """Create an Ogre monster (CR 2)."""
    ogre = Monster("Ogre", challenge_rating=2)
    
    # Basic stats
    ogre.size = Size.LARGE
    ogre.monster_type = MonsterType.GIANT
    ogre.alignment = "chaotic evil"
    
    # Ability scores
    ogre.ability_scores = AbilityScores(
        strength=19, dexterity=8, constitution=16,
        intelligence=5, wisdom=7, charisma=7
    )
    
    # Hit points and AC
    ogre.hit_points = HitPoints(maximum=59, current=59)
    ogre.armor_class = ArmorClass(
        base=10,
        armor_bonus=1,  # Hide armor
        dex_modifier=ogre.get_ability_modifier(AbilityType.DEXTERITY),
        natural_armor=0
    )
    
    ogre.speed = 40
    ogre.darkvision = 60
    ogre.passive_perception = 8
    ogre.languages = ["Common", "Giant"]
    
    # Attacks
    greatclub = Attack(
        name="Greatclub",
        attack_bonus=6,
        damage_dice="2d8",
        damage_type="bludgeoning",
        damage_bonus=4,
        reach=5
    )
    
    javelin = Attack(
        name="Javelin",
        attack_bonus=6,
        damage_dice="2d6",
        damage_type="piercing",
        damage_bonus=4,
        reach=5,
        range_normal=30,
        range_long=120
    )
    
    ogre.add_attack(greatclub)
    ogre.add_attack(javelin)
    
    return ogre

# Monster registry
MONSTER_REGISTRY: Dict[str, callable] = {
    "Goblin": create_goblin,
    "Orc": create_orc,
    "Wolf": create_wolf,
    "Skeleton": create_skeleton,
    "Ogre": create_ogre,
}

def get_available_monsters() -> List[str]:
    """Get list of available monster names."""
    return list(MONSTER_REGISTRY.keys())

def create_monster(monster_name: str) -> Monster:
    """Create a monster by name."""
    if monster_name not in MONSTER_REGISTRY:
        raise ValueError(f"Unknown monster: {monster_name}")
    
    return MONSTER_REGISTRY[monster_name]()

def get_monsters_by_cr(min_cr: float = 0, max_cr: float = 30) -> List[str]:
    """Get monsters within a challenge rating range."""
    monsters = []
    for name, creator_func in MONSTER_REGISTRY.items():
        monster = creator_func()
        if min_cr <= monster.challenge_rating <= max_cr:
            monsters.append(name)
    return monsters
