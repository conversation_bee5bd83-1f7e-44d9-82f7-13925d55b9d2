#!/usr/bin/env python3
"""
Simple test script for the D&D Combat Simulator
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.character import Character, CharacterClass
from src.core.monster import Monster
from src.core.combat import CombatEngine
from src.core.abilities import AbilityScores, AbilityType
from src.data.monsters import create_goblin, create_orc
from src.data.sample_characters import create_sample_fighter, create_sample_wizard
from src.ai.combat_ai import AutoCombat

def test_basic_combat():
    """Test a basic combat scenario."""
    print("=== D&D Combat Simulator Test ===\n")
    
    # Create characters
    print("Creating characters...")
    fighter = create_sample_fighter()
    wizard = create_sample_wizard()
    print(f"Created {fighter.name} (Fighter Level {fighter.level})")
    print(f"Created {wizard.name} (Wizard Level {wizard.level})")
    
    # Create monsters
    print("\nCreating monsters...")
    goblin1 = create_goblin()
    goblin1.name = "Goblin 1"
    goblin2 = create_goblin()
    goblin2.name = "Goblin 2"
    orc = create_orc()
    print(f"Created {goblin1.name} (CR {goblin1.challenge_rating})")
    print(f"Created {goblin2.name} (CR {goblin2.challenge_rating})")
    print(f"Created {orc.name} (CR {orc.challenge_rating})")
    
    # Set up combat
    print("\nSetting up combat...")
    combat_engine = CombatEngine()
    
    # Add participants
    combat_engine.add_participant(fighter)
    combat_engine.add_participant(wizard)
    combat_engine.add_participant(goblin1)
    combat_engine.add_participant(goblin2)
    combat_engine.add_participant(orc)
    
    print(f"Added {len(combat_engine.participants)} participants to combat")
    
    # Start combat
    print("\nStarting combat...")
    combat_engine.start_combat()
    
    # Show initial state
    print(f"Initiative order: {[p.name for p in combat_engine.initiative_order]}")
    print(f"Current round: {combat_engine.current_round}")
    
    # Run auto combat
    print("\nRunning automated combat...")
    auto_combat = AutoCombat(combat_engine)
    success = auto_combat.run_full_simulation()
    
    if success:
        print("Combat completed successfully!")
    else:
        print("Combat stopped due to round limit.")
    
    # Show results
    print(f"\nCombat Results:")
    print(f"Total rounds: {combat_engine.current_round}")
    print(f"Combat state: {combat_engine.state.value}")
    
    print("\nFinal participant status:")
    for participant in combat_engine.participants:
        status = "ALIVE" if participant.is_alive() else "DEAD"
        hp_status = f"{participant.hit_points.current}/{participant.hit_points.maximum} HP"
        print(f"  {participant.name}: {status} ({hp_status})")
    
    print(f"\nCombat log ({len(combat_engine.combat_log)} entries):")
    for i, message in enumerate(combat_engine.combat_log[-10:], 1):  # Show last 10 messages
        print(f"  {i}. {message}")
    
    return True

def test_dice_system():
    """Test the dice rolling system."""
    print("\n=== Testing Dice System ===")
    
    from src.core.dice import roll_d20, roll_damage, d6, AdvantageType
    
    # Test basic rolls
    print("Basic d20 roll:", roll_d20())
    print("d20 with +5 modifier:", roll_d20(5))
    print("d20 with advantage:", roll_d20(0, AdvantageType.ADVANTAGE))
    print("d20 with disadvantage:", roll_d20(0, AdvantageType.DISADVANTAGE))
    
    # Test damage rolls
    print("1d8+3 damage:", roll_damage("1d8", 3))
    print("2d6 damage:", roll_damage("2d6"))
    print("Basic d6:", d6())
    
    return True

def test_character_creation():
    """Test character creation and serialization."""
    print("\n=== Testing Character System ===")
    
    # Create a character
    char = Character("Test Character", level=5, character_class=CharacterClass.ROGUE)
    char.ability_scores = AbilityScores(
        strength=12, dexterity=16, constitution=14,
        intelligence=13, wisdom=12, charisma=10
    )
    
    print(f"Created character: {char.name}")
    print(f"Level: {char.level}, Class: {char.character_class.value}")
    print(f"Dexterity: {char.ability_scores.dexterity} (modifier: {char.get_ability_modifier(AbilityType.DEXTERITY)})")
    print(f"AC: {char.armor_class.total}")
    print(f"HP: {char.hit_points.current}/{char.hit_points.maximum}")
    
    # Test serialization
    char_dict = char.to_dict()
    print(f"Serialized character has {len(char_dict)} fields")
    
    # Test deserialization
    char2 = Character.from_dict(char_dict)
    print(f"Deserialized character: {char2.name} (Level {char2.level})")
    
    return True

def main():
    """Run all tests."""
    try:
        print("D&D Combat Simulator - Test Suite")
        print("=" * 50)
        
        # Run tests
        test_dice_system()
        test_character_creation()
        test_basic_combat()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
