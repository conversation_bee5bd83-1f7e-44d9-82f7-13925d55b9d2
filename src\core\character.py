"""
Character class for D&D Combat Simulator
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json

from src.core.abilities import AbilityScores, AbilityType, SavingThrows
from src.core.conditions import ConditionManager, ConditionType, Condition
from src.core.dice import roll_d20, roll_initiative, AdvantageType

class CharacterClass(Enum):
    """D&D character classes."""
    BARBARIAN = "barbarian"
    BARD = "bard"
    CLERIC = "cleric"
    DRUID = "druid"
    FIGHTER = "fighter"
    MONK = "monk"
    PALADIN = "paladin"
    RANGER = "ranger"
    ROGUE = "rogue"
    SORCERER = "sorcerer"
    WARLOCK = "warlock"
    WIZARD = "wizard"

class Size(Enum):
    """Creature sizes."""
    TINY = "tiny"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    HUGE = "huge"
    GARGANTUAN = "gargantuan"

@dataclass
class HitPoints:
    """Manages hit points and temporary hit points."""
    maximum: int
    current: int
    temporary: int = 0
    
    def __post_init__(self):
        if self.current > self.maximum:
            self.current = self.maximum
    
    @property
    def total(self) -> int:
        """Total effective hit points including temporary."""
        return self.current + self.temporary
    
    def take_damage(self, damage: int) -> int:
        """
        Apply damage to hit points.
        
        Args:
            damage: Amount of damage to take
            
        Returns:
            Actual damage taken after temporary HP
        """
        if damage <= 0:
            return 0
            
        original_total = self.total
        
        # Apply damage to temporary HP first
        if self.temporary > 0:
            if damage >= self.temporary:
                damage -= self.temporary
                self.temporary = 0
            else:
                self.temporary -= damage
                damage = 0
        
        # Apply remaining damage to current HP
        if damage > 0:
            self.current = max(0, self.current - damage)
        
        return original_total - self.total
    
    def heal(self, healing: int) -> int:
        """
        Heal hit points.
        
        Args:
            healing: Amount of healing
            
        Returns:
            Actual healing applied
        """
        if healing <= 0:
            return 0
            
        old_current = self.current
        self.current = min(self.maximum, self.current + healing)
        return self.current - old_current
    
    def add_temporary_hp(self, temp_hp: int):
        """Add temporary hit points (doesn't stack, takes higher value)."""
        if temp_hp > self.temporary:
            self.temporary = temp_hp
    
    def is_alive(self) -> bool:
        """Check if the creature is alive (has hit points)."""
        return self.total > 0
    
    def is_unconscious(self) -> bool:
        """Check if the creature is unconscious (0 current HP)."""
        return self.current <= 0

@dataclass
class ArmorClass:
    """Manages armor class calculation."""
    base: int = 10
    armor_bonus: int = 0
    shield_bonus: int = 0
    dex_modifier: int = 0
    natural_armor: int = 0
    misc_bonus: int = 0
    
    @property
    def total(self) -> int:
        """Calculate total armor class."""
        return (self.base + self.armor_bonus + self.shield_bonus + 
                self.dex_modifier + self.natural_armor + self.misc_bonus)

class Character:
    """Represents a D&D character or NPC."""
    
    def __init__(self, name: str, level: int = 1, character_class: CharacterClass = CharacterClass.FIGHTER):
        self.name = name
        self.level = level
        self.character_class = character_class
        self.size = Size.MEDIUM
        
        # Core stats
        self.ability_scores = AbilityScores()
        self.hit_points = HitPoints(maximum=10, current=10)
        self.armor_class = ArmorClass()
        self.speed = 30
        
        # Combat stats
        self.proficiency_bonus = self._calculate_proficiency_bonus()
        self.saving_throws = SavingThrows()
        self.conditions = ConditionManager()
        
        # Initiative and combat state
        self.initiative = 0
        self.has_acted_this_turn = False
        self.has_bonus_action = True
        self.has_reaction = True
        self.movement_remaining = self.speed
        
        # Death saves
        self.death_save_successes = 0
        self.death_save_failures = 0
        
    def _calculate_proficiency_bonus(self) -> int:
        """Calculate proficiency bonus based on level."""
        return 2 + ((self.level - 1) // 4)
    
    def update_level(self, new_level: int):
        """Update character level and recalculate dependent stats."""
        self.level = max(1, min(20, new_level))
        self.proficiency_bonus = self._calculate_proficiency_bonus()
    
    def get_ability_modifier(self, ability: AbilityType) -> int:
        """Get the modifier for a specific ability."""
        return self.ability_scores.get_modifier(ability)
    
    def get_saving_throw_bonus(self, ability: AbilityType) -> int:
        """Get the total saving throw bonus for an ability."""
        return self.saving_throws.get_save_bonus(ability, self.ability_scores, self.proficiency_bonus)
    
    def roll_initiative(self) -> int:
        """Roll initiative and store the result."""
        dex_mod = self.get_ability_modifier(AbilityType.DEXTERITY)
        roll = roll_initiative(dex_mod)
        self.initiative = roll.total
        return self.initiative
    
    def start_turn(self):
        """Reset turn-based resources at the start of a turn."""
        self.has_acted_this_turn = False
        self.has_bonus_action = True
        self.has_reaction = True
        self.movement_remaining = self.speed
        
        # Tick condition durations
        self.conditions.tick_all_durations()
    
    def end_turn(self):
        """Mark the end of the character's turn."""
        self.has_acted_this_turn = True
    
    def can_take_actions(self) -> bool:
        """Check if the character can take actions."""
        return self.conditions.can_take_actions() and self.hit_points.is_alive()
    
    def can_move(self) -> bool:
        """Check if the character can move."""
        return self.conditions.can_move() and self.hit_points.is_alive()
    
    def take_damage(self, damage: int) -> int:
        """Apply damage and handle unconsciousness."""
        actual_damage = self.hit_points.take_damage(damage)
        
        if self.hit_points.current <= 0 and self.hit_points.is_alive():
            # Character is unconscious but not dead
            unconscious = Condition(ConditionType.UNCONSCIOUS)
            self.conditions.add_condition(unconscious)
        
        return actual_damage
    
    def heal(self, healing: int) -> int:
        """Heal the character and potentially remove unconsciousness."""
        actual_healing = self.hit_points.heal(healing)
        
        if self.hit_points.current > 0:
            # Remove unconscious condition if healed above 0 HP
            self.conditions.remove_condition(ConditionType.UNCONSCIOUS)
            self.death_save_successes = 0
            self.death_save_failures = 0
        
        return actual_healing
    
    def is_alive(self) -> bool:
        """Check if the character is alive."""
        return self.hit_points.is_alive() and self.death_save_failures < 3
    
    def is_unconscious(self) -> bool:
        """Check if the character is unconscious."""
        return self.hit_points.is_unconscious() or self.conditions.has_condition(ConditionType.UNCONSCIOUS)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert character to dictionary for serialization."""
        return {
            "name": self.name,
            "level": self.level,
            "character_class": self.character_class.value,
            "size": self.size.value,
            "ability_scores": self.ability_scores.to_dict(),
            "hit_points": {
                "maximum": self.hit_points.maximum,
                "current": self.hit_points.current,
                "temporary": self.hit_points.temporary
            },
            "armor_class": {
                "base": self.armor_class.base,
                "armor_bonus": self.armor_class.armor_bonus,
                "shield_bonus": self.armor_class.shield_bonus,
                "dex_modifier": self.armor_class.dex_modifier,
                "natural_armor": self.armor_class.natural_armor,
                "misc_bonus": self.armor_class.misc_bonus
            },
            "speed": self.speed,
            "saving_throws": self.saving_throws.to_dict(),
            "conditions": self.conditions.to_dict(),
            "death_save_successes": self.death_save_successes,
            "death_save_failures": self.death_save_failures
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """Create character from dictionary."""
        char = cls(
            name=data["name"],
            level=data.get("level", 1),
            character_class=CharacterClass(data.get("character_class", "fighter"))
        )

        char.size = Size(data.get("size", "medium"))
        char.ability_scores = AbilityScores.from_dict(data.get("ability_scores", {}))

        # Hit points
        hp_data = data.get("hit_points", {})
        char.hit_points = HitPoints(
            maximum=hp_data.get("maximum", 10),
            current=hp_data.get("current", 10),
            temporary=hp_data.get("temporary", 0)
        )

        # Armor class
        ac_data = data.get("armor_class", {})
        char.armor_class = ArmorClass(
            base=ac_data.get("base", 10),
            armor_bonus=ac_data.get("armor_bonus", 0),
            shield_bonus=ac_data.get("shield_bonus", 0),
            dex_modifier=ac_data.get("dex_modifier", 0),
            natural_armor=ac_data.get("natural_armor", 0),
            misc_bonus=ac_data.get("misc_bonus", 0)
        )

        char.speed = data.get("speed", 30)
        char.saving_throws = SavingThrows.from_dict(data.get("saving_throws", {}))
        char.conditions = ConditionManager.from_dict(data.get("conditions", {}))
        char.death_save_successes = data.get("death_save_successes", 0)
        char.death_save_failures = data.get("death_save_failures", 0)

        return char

    def save_to_file(self, filename: str):
        """Save character to JSON file."""
        with open(filename, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def load_from_file(cls, filename: str) -> 'Character':
        """Load character from JSON file."""
        with open(filename, 'r') as f:
            data = json.load(f)
        return cls.from_dict(data)
