# D&D Combat Simulator - Requirements Specification

## Project Name
**D&D Combat Simulator**

## Overview

This Windows desktop application is a turn-based combat simulator built for Dungeons & Dragons 2024 rules. It supports both player-focused and DM-focused simulation. Players can upload their characters (ideally from D&D Beyond) and practice combat, while DMs can simulate party vs. monster encounters to assess difficulty and balance.

## Purpose

The primary goals of the app are:
- Allow players to simulate 1v1 and party-based D&D combat to practice action economy, spell usage, and survivability.
- Allow DMs to simulate encounters with different types and quantities of monsters to test balance and CR scaling.
- Enable both full simulation (auto-run) and step-through manual modes with visual turn logs.

## Key Features

### 🧑‍🤝‍🧑 Users
- **Players** can simulate combat using imported or manually created character sheets.
- **DMs** can build encounters using monster stat blocks and simulate party performance against different configurations.

### ⚔️ Simulation Modes
- 1v1 (PC vs Monster)
- Party vs Encounter
- Full Auto-Run (basic AI controls all entities)
- Player-Controlled Turn-by-Turn (users pick actions/spells manually)
- Pause-and-Adjust mid simulation
- Step-through for learning/training purposes

### 🎲 Combat System
Full implementation of 2024 D&D combat rules:
- Turn-based initiative order
- Action economy (action, bonus action, reaction, movement)
- Spell casting (including concentration, saving throws, counters)
- Weapon attacks and damage rolls
- Advantage/Disadvantage
- Status effects and conditions
- Death saves, unconsciousness, saving throws
- Monster stat block handling and recharge mechanics

## Character Integration

### Input Options
- Manual entry of PC stats
- D&D Beyond integration:
  - Preferred: ability to import JSON export (if available)
  - Stretch: API-based import (requires user authentication)
- Ability to store and reuse characters and party setups

## Monster Integration

- Manual monster creation or selection from included SRD-based stat blocks
- CR calculator to recommend appropriate encounter difficulty based on party strength
- Monsters follow full turn-based combat logic, controlled by AI or optionally by user

## Interface

### GUI (Non-Graphical)
- Windows native GUI (using PyQt5 / Tkinter / custom framework)
- No 3D or sprite-based visuals required
- Core visual elements:
  - Stat blocks and spell lists
  - HP tracker and change log
  - Turn order and initiative display
  - Combat log with rolls and outcomes
  - Action/spell selector for manual modes

## AI-Controlled Logic

- Basic combat AI for monsters and party members in full auto-sim mode:
  - Prioritizes attacking lower HP targets
  - Chooses spells or attacks based on range and effectiveness
  - Uses healing or disengage actions when appropriate
- Toggle: allow manual control of monster actions

## Input/Output Expectations

### Input:
- Character sheets (manual or import)
- Monster selection from list or SRD
- Simulation settings (mode, number of rounds, AI/manual control toggles)

### Output:
- Step-by-step combat log
- Summary of key stats: damage dealt, healing, spell usage, status effects
- Victory/defeat state
- Option to export combat logs

## Technical Specs

- **Platform**: Windows Desktop
- **Language**: Python
- **Dependencies**: 
  - GUI: PyQt5 or Tkinter (preferred lightweight)
  - JSON handling
  - Dice rolling module (custom or use existing like `d20`)
  - Optional: SQLite or local JSON for character/party storage
- **Execution**: Local only, runs from folder, no installation required

## Stretch Goals / Future Enhancements

- DNDBeyond API OAuth integration (if accessible)
- Save/load campaign history
- Encounter suggestions based on XP budgets
- Advanced AI scripting system for smarter monster behavior
- Multi-player "hot seat" control
- Encounter randomizer tool

## Known Limitations

- No graphics beyond simple GUI elements
- Rules limited to 2024 D&D system (no legacy rules support)
- Only supports desktop (not web or mobile)
- No external rulesets (Pathfinder, etc.)

## Appendix: Definitions

- **Full Sim Mode**: AI controls all actions, simulates battle to completion
- **Step-Through Mode**: Each turn is paused for review or manual input
- **CR**: Challenge Rating, used by DMs to gauge monster difficulty
- **SRD**: System Reference Document, the open license content from WOTC

---

