#!/usr/bin/env python3
"""
Demo script showing D&D Combat Simulator capabilities
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.character import Character, CharacterClass
from src.core.combat import CombatEngine
from src.data.monsters import create_goblin, create_orc, create_ogre
from src.data.sample_characters import create_sample_party
from src.ai.combat_ai import AutoCombat

def demo_party_vs_goblins():
    """Demo: Full party vs multiple goblins."""
    print("=== DEMO: Party vs Goblins ===")
    print("A balanced 4-character party faces off against 4 goblins")
    print()
    
    # Create party
    party = create_sample_party()
    print("Party:")
    for char in party:
        print(f"  - {char.name} (Level {char.level} {char.character_class.value.title()}) - AC {char.armor_class.total}, HP {char.hit_points.maximum}")
    
    # Create goblins
    goblins = []
    for i in range(4):
        goblin = create_goblin()
        goblin.name = f"Goblin {i+1}"
        goblins.append(goblin)
    
    print(f"\nEnemies:")
    for goblin in goblins:
        print(f"  - {goblin.name} (CR {goblin.challenge_rating}) - AC {goblin.armor_class.total}, HP {goblin.hit_points.maximum}")
    
    # Set up combat
    combat_engine = CombatEngine()
    for char in party:
        combat_engine.add_participant(char)
    for goblin in goblins:
        combat_engine.add_participant(goblin)
    
    # Run combat
    print(f"\nStarting combat...")
    combat_engine.start_combat()
    
    auto_combat = AutoCombat(combat_engine)
    auto_combat.run_full_simulation()
    
    # Show results
    print(f"\nCombat ended after {combat_engine.current_round} rounds!")
    
    alive_party = [p for p in party if p.is_alive()]
    alive_goblins = [g for g in goblins if g.is_alive()]
    
    if alive_party and not alive_goblins:
        print("🎉 VICTORY! The party defeated all goblins!")
    elif alive_goblins and not alive_party:
        print("💀 DEFEAT! The goblins overwhelmed the party!")
    else:
        print("⚔️ STALEMATE! Some from both sides survived!")
    
    print(f"\nSurvivors:")
    for participant in combat_engine.participants:
        if participant.is_alive():
            hp = f"{participant.hit_points.current}/{participant.hit_points.maximum}"
            print(f"  - {participant.name}: {hp} HP")
    
    return len(alive_party) > 0

def demo_solo_vs_ogre():
    """Demo: Single character vs ogre."""
    print("\n=== DEMO: Fighter vs Ogre ===")
    print("A lone fighter faces a dangerous ogre")
    print()
    
    # Create fighter
    from src.data.sample_characters import create_sample_fighter
    fighter = create_sample_fighter()
    
    # Create ogre
    ogre = create_ogre()
    
    print(f"Hero: {fighter.name} (Level {fighter.level}) - AC {fighter.armor_class.total}, HP {fighter.hit_points.maximum}")
    print(f"Enemy: {ogre.name} (CR {ogre.challenge_rating}) - AC {ogre.armor_class.total}, HP {ogre.hit_points.maximum}")
    
    # Set up combat
    combat_engine = CombatEngine()
    combat_engine.add_participant(fighter)
    combat_engine.add_participant(ogre)
    
    # Run combat
    print(f"\nStarting combat...")
    combat_engine.start_combat()
    
    auto_combat = AutoCombat(combat_engine)
    auto_combat.run_full_simulation()
    
    # Show results
    print(f"\nCombat ended after {combat_engine.current_round} rounds!")
    
    if fighter.is_alive():
        print(f"🎉 VICTORY! {fighter.name} defeated the ogre!")
        print(f"   Remaining HP: {fighter.hit_points.current}/{fighter.hit_points.maximum}")
    else:
        print(f"💀 DEFEAT! The ogre was too powerful!")
        if ogre.is_alive():
            print(f"   Ogre remaining HP: {ogre.hit_points.current}/{ogre.hit_points.maximum}")
    
    return fighter.is_alive()

def demo_encounter_balance():
    """Demo: Testing encounter balance."""
    print("\n=== DEMO: Encounter Balance Testing ===")
    print("Testing different encounter difficulties against the same party")
    print()
    
    party = create_sample_party()
    encounters = [
        ("Easy", [create_goblin()]),
        ("Medium", [create_orc(), create_goblin()]),
        ("Hard", [create_ogre()]),
        ("Deadly", [create_ogre(), create_orc()])
    ]
    
    results = {}
    
    for difficulty, monsters in encounters:
        print(f"\nTesting {difficulty} encounter...")
        
        # Reset party health
        for char in party:
            char.hit_points.current = char.hit_points.maximum
            char.conditions.clear_all()
            char.death_save_successes = 0
            char.death_save_failures = 0
        
        # Reset monster health
        for monster in monsters:
            monster.hit_points.current = monster.hit_points.maximum
            monster.conditions.clear_all()
        
        # Set up combat
        combat_engine = CombatEngine()
        for char in party:
            combat_engine.add_participant(char)
        for monster in monsters:
            combat_engine.add_participant(monster)
        
        # Run combat
        combat_engine.start_combat()
        auto_combat = AutoCombat(combat_engine)
        auto_combat.run_full_simulation()
        
        # Record results
        alive_party = [p for p in party if p.is_alive()]
        party_hp_remaining = sum(p.hit_points.current for p in alive_party)
        total_party_hp = sum(p.hit_points.maximum for p in party)
        
        results[difficulty] = {
            'survivors': len(alive_party),
            'rounds': combat_engine.current_round,
            'hp_remaining': party_hp_remaining,
            'hp_percentage': (party_hp_remaining / total_party_hp) * 100
        }
        
        if len(alive_party) == len(party):
            outcome = "VICTORY"
        elif len(alive_party) > 0:
            outcome = "PYRRHIC VICTORY"
        else:
            outcome = "DEFEAT"
        
        print(f"  Result: {outcome} in {combat_engine.current_round} rounds")
        print(f"  Survivors: {len(alive_party)}/{len(party)}")
        if party_hp_remaining > 0:
            print(f"  Party HP remaining: {party_hp_remaining}/{total_party_hp} ({party_hp_remaining/total_party_hp*100:.1f}%)")
    
    print(f"\n=== ENCOUNTER BALANCE SUMMARY ===")
    for difficulty, result in results.items():
        print(f"{difficulty:8}: {result['survivors']}/4 survivors, {result['rounds']} rounds, {result['hp_percentage']:.1f}% HP remaining")

def main():
    """Run all demos."""
    print("D&D Combat Simulator - Demo")
    print("=" * 50)
    print("This demo shows the combat simulator in action with different scenarios.")
    print()
    
    try:
        # Run demos
        victory1 = demo_party_vs_goblins()
        victory2 = demo_solo_vs_ogre()
        demo_encounter_balance()
        
        print("\n" + "=" * 50)
        print("Demo completed!")
        print()
        print("To use the full GUI application, run: python main.py")
        print()
        print("Features demonstrated:")
        print("✓ Character creation and management")
        print("✓ Monster database with SRD creatures")
        print("✓ Turn-based combat with initiative")
        print("✓ Automated AI combat decisions")
        print("✓ Damage calculation and status tracking")
        print("✓ Encounter balance testing")
        print("✓ Multiple combat scenarios")
        
        return True
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
