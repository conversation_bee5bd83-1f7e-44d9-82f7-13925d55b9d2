"""
Character attack system for D&D Combat Simulator
"""

from typing import List, Dict, Optional, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum

from src.core.weapons import WeaponData, WeaponProperty, DamageType
from src.core.abilities import AbilityType
from src.core.dice import roll_d20, roll_damage, AdvantageType, DiceRoll

class AttackType(Enum):
    """Types of attacks."""
    WEAPON_ATTACK = "weapon_attack"
    SPELL_ATTACK = "spell_attack"
    UNARMED_STRIKE = "unarmed_strike"
    NATURAL_WEAPON = "natural_weapon"

@dataclass
class CharacterAttack:
    """Represents a character's attack option."""
    name: str
    attack_type: AttackType
    ability_modifier: AbilityType
    damage_dice: str
    damage_type: DamageType
    damage_ability: AbilityType
    
    # Optional properties
    weapon_data: Optional[WeaponData] = None
    magic_bonus: int = 0
    additional_damage: str = ""
    additional_damage_type: Optional[DamageType] = None
    save_dc: Optional[int] = None
    save_ability: Optional[AbilityType] = None
    range_normal: Optional[int] = None
    range_long: Optional[int] = None
    description: str = ""
    
    def calculate_attack_bonus(self, character) -> int:
        """Calculate the total attack bonus for this attack."""
        from src.core.character import Character
        
        # Base ability modifier
        ability_mod = character.get_ability_modifier(self.ability_modifier)
        
        # Proficiency bonus (assume proficient for now)
        prof_bonus = character.proficiency_bonus
        
        # Magic bonus
        magic_bonus = self.magic_bonus
        
        return ability_mod + prof_bonus + magic_bonus
    
    def calculate_damage_bonus(self, character) -> int:
        """Calculate the damage bonus for this attack."""
        from src.core.character import Character
        
        # Base ability modifier
        ability_mod = character.get_ability_modifier(self.damage_ability)
        
        # Magic bonus
        magic_bonus = self.magic_bonus
        
        return ability_mod + magic_bonus
    
    def make_attack_roll(self, character, target_ac: int, advantage: AdvantageType = AdvantageType.NORMAL) -> Tuple[bool, DiceRoll]:
        """Make an attack roll against a target."""
        attack_bonus = self.calculate_attack_bonus(character)
        attack_roll = roll_d20(attack_bonus, advantage)
        
        # Check for critical hit (natural 20)
        is_crit = any(roll == 20 for roll in attack_roll.rolls)
        
        # Check if attack hits
        hits = attack_roll.total >= target_ac or is_crit
        
        return hits, attack_roll
    
    def roll_damage(self, character, is_critical: bool = False) -> Tuple[DiceRoll, Optional[DiceRoll]]:
        """Roll damage for this attack."""
        damage_bonus = self.calculate_damage_bonus(character)
        
        # Main damage
        main_damage = roll_damage(self.damage_dice, damage_bonus)
        
        # Critical hit doubles dice
        if is_critical:
            crit_damage = roll_damage(self.damage_dice, 0)  # No bonus on crit dice
            main_damage.total += crit_damage.total
            main_damage.rolls.extend(crit_damage.rolls)
        
        # Additional damage (like sneak attack, smite, etc.)
        additional_damage = None
        if self.additional_damage:
            additional_damage = roll_damage(self.additional_damage, 0)
            if is_critical:
                # Additional damage dice are also doubled on crit
                crit_additional = roll_damage(self.additional_damage, 0)
                additional_damage.total += crit_additional.total
                additional_damage.rolls.extend(crit_additional.rolls)
        
        return main_damage, additional_damage
    
    def get_range(self) -> Tuple[Optional[int], Optional[int]]:
        """Get the normal and long range for this attack."""
        if self.weapon_data:
            return self.weapon_data.range_normal, self.weapon_data.range_long
        return self.range_normal, self.range_long
    
    def is_ranged_attack(self) -> bool:
        """Check if this is a ranged attack."""
        normal_range, _ = self.get_range()
        return normal_range is not None
    
    def uses_finesse(self) -> bool:
        """Check if this attack can use finesse."""
        if self.weapon_data:
            return self.weapon_data.has_property(WeaponProperty.FINESSE)
        return False

def create_weapon_attack(weapon_data: WeaponData, magic_bonus: int = 0, 
                        custom_name: Optional[str] = None) -> CharacterAttack:
    """Create a character attack from weapon data."""
    name = custom_name or weapon_data.name
    
    # Determine ability modifier based on weapon properties
    if weapon_data.has_property(WeaponProperty.FINESSE):
        # Finesse weapons can use DEX or STR (we'll let the character choose)
        ability_modifier = AbilityType.DEXTERITY
        damage_ability = AbilityType.DEXTERITY
    elif weapon_data.is_ranged():
        # Ranged weapons use DEX
        ability_modifier = AbilityType.DEXTERITY
        damage_ability = AbilityType.DEXTERITY
    else:
        # Melee weapons use STR
        ability_modifier = AbilityType.STRENGTH
        damage_ability = AbilityType.STRENGTH
    
    return CharacterAttack(
        name=name,
        attack_type=AttackType.WEAPON_ATTACK,
        ability_modifier=ability_modifier,
        damage_dice=weapon_data.damage_dice,
        damage_type=weapon_data.damage_type,
        damage_ability=damage_ability,
        weapon_data=weapon_data,
        magic_bonus=magic_bonus,
        range_normal=weapon_data.range_normal,
        range_long=weapon_data.range_long
    )

def create_unarmed_strike(character_level: int = 1) -> CharacterAttack:
    """Create an unarmed strike attack."""
    # Monks get improved unarmed damage
    if character_level >= 17:
        damage_dice = "1d10"
    elif character_level >= 11:
        damage_dice = "1d8"
    elif character_level >= 5:
        damage_dice = "1d6"
    else:
        damage_dice = "1d4"  # Default for monks, 1 + STR for others
    
    return CharacterAttack(
        name="Unarmed Strike",
        attack_type=AttackType.UNARMED_STRIKE,
        ability_modifier=AbilityType.STRENGTH,
        damage_dice=damage_dice,
        damage_type=DamageType.BLUDGEONING,
        damage_ability=AbilityType.STRENGTH,
        description="A punch, kick, or other unarmed attack"
    )

def create_spell_attack(name: str, damage_dice: str, damage_type: DamageType,
                       spell_attack: bool = True, save_dc: Optional[int] = None,
                       save_ability: Optional[AbilityType] = None,
                       range_normal: Optional[int] = None) -> CharacterAttack:
    """Create a spell attack."""
    if spell_attack:
        # Spell attack roll
        return CharacterAttack(
            name=name,
            attack_type=AttackType.SPELL_ATTACK,
            ability_modifier=AbilityType.INTELLIGENCE,  # Will be overridden by spellcasting class
            damage_dice=damage_dice,
            damage_type=damage_type,
            damage_ability=AbilityType.INTELLIGENCE,
            range_normal=range_normal,
            description=f"Spell attack: {name}"
        )
    else:
        # Saving throw spell
        return CharacterAttack(
            name=name,
            attack_type=AttackType.SPELL_ATTACK,
            ability_modifier=AbilityType.INTELLIGENCE,
            damage_dice=damage_dice,
            damage_type=damage_type,
            damage_ability=AbilityType.INTELLIGENCE,
            save_dc=save_dc,
            save_ability=save_ability,
            range_normal=range_normal,
            description=f"Saving throw spell: {name}"
        )

# Common attack templates
def get_common_attacks() -> Dict[str, CharacterAttack]:
    """Get a dictionary of common attacks."""
    from src.core.weapons import get_weapon
    
    attacks = {}
    
    # Common weapons
    common_weapons = ["Dagger", "Shortsword", "Longsword", "Greatsword", "Shortbow", "Longbow"]
    for weapon_name in common_weapons:
        weapon_data = get_weapon(weapon_name)
        if weapon_data:
            attacks[weapon_name] = create_weapon_attack(weapon_data)
    
    # Unarmed strike
    attacks["Unarmed Strike"] = create_unarmed_strike()
    
    # Common cantrips
    attacks["Fire Bolt"] = create_spell_attack(
        "Fire Bolt", "1d10", DamageType.FIRE, spell_attack=True, range_normal=120
    )
    attacks["Eldritch Blast"] = create_spell_attack(
        "Eldritch Blast", "1d10", DamageType.FORCE, spell_attack=True, range_normal=120
    )
    
    return attacks
