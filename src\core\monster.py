"""
Monster class for D&D Combat Simulator
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

from .character import Character, Size, HitPoints, ArmorClass
from .abilities import AbilityScores, AbilityType, SavingThrows
from .conditions import ConditionManager
from .dice import roll_damage, DiceRoll

class MonsterType(Enum):
    """Monster types from D&D."""
    ABERRATION = "aberration"
    BEAST = "beast"
    CELESTIAL = "celestial"
    CONSTRUCT = "construct"
    DRAGON = "dragon"
    ELEMENTAL = "elemental"
    FEY = "fey"
    FIEND = "fiend"
    GIANT = "giant"
    HUMANOID = "humanoid"
    MONSTROSITY = "monstrosity"
    OOZE = "ooze"
    PLANT = "plant"
    UNDEAD = "undead"

@dataclass
class Attack:
    """Represents a monster attack."""
    name: str
    attack_bonus: int
    damage_dice: str
    damage_type: str
    damage_bonus: int = 0
    reach: int = 5
    range_normal: Optional[int] = None
    range_long: Optional[int] = None
    description: str = ""
    
    def make_attack(self, target_ac: int) -> tuple[bool, Optional[DiceRoll]]:
        """
        Make an attack roll against a target.
        
        Args:
            target_ac: Target's armor class
            
        Returns:
            Tuple of (hit, damage_roll). damage_roll is None if miss.
        """
        from .dice import roll_attack, AdvantageType
        
        attack_roll = roll_attack(self.attack_bonus, AdvantageType.NORMAL)
        hit = attack_roll.total >= target_ac
        
        if hit:
            damage_roll = roll_damage(self.damage_dice, self.damage_bonus)
            return True, damage_roll
        else:
            return False, None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "attack_bonus": self.attack_bonus,
            "damage_dice": self.damage_dice,
            "damage_type": self.damage_type,
            "damage_bonus": self.damage_bonus,
            "reach": self.reach,
            "range_normal": self.range_normal,
            "range_long": self.range_long,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Attack':
        """Create from dictionary."""
        return cls(
            name=data["name"],
            attack_bonus=data["attack_bonus"],
            damage_dice=data["damage_dice"],
            damage_type=data["damage_type"],
            damage_bonus=data.get("damage_bonus", 0),
            reach=data.get("reach", 5),
            range_normal=data.get("range_normal"),
            range_long=data.get("range_long"),
            description=data.get("description", "")
        )

@dataclass
class SpecialAbility:
    """Represents a monster special ability."""
    name: str
    description: str
    recharge: Optional[str] = None  # e.g., "5-6", "short rest", "long rest"
    uses_per_day: Optional[int] = None
    used_today: int = 0
    
    def can_use(self) -> bool:
        """Check if the ability can be used."""
        if self.uses_per_day is not None:
            return self.used_today < self.uses_per_day
        return True
    
    def use_ability(self):
        """Mark the ability as used."""
        if self.uses_per_day is not None:
            self.used_today += 1
    
    def recharge_check(self) -> bool:
        """Check if a recharge ability recharges (roll d6)."""
        if self.recharge and "-" in self.recharge:
            from .dice import d6
            roll = d6()
            min_roll = int(self.recharge.split("-")[0])
            return roll.total >= min_roll
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "recharge": self.recharge,
            "uses_per_day": self.uses_per_day,
            "used_today": self.used_today
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SpecialAbility':
        """Create from dictionary."""
        return cls(
            name=data["name"],
            description=data["description"],
            recharge=data.get("recharge"),
            uses_per_day=data.get("uses_per_day"),
            used_today=data.get("used_today", 0)
        )

class Monster(Character):
    """Represents a D&D monster, extending the Character class."""
    
    def __init__(self, name: str, challenge_rating: float = 0.125):
        super().__init__(name, level=1)
        
        # Monster-specific attributes
        self.challenge_rating = challenge_rating
        self.monster_type = MonsterType.BEAST
        self.alignment = "neutral"
        
        # Combat abilities
        self.attacks: List[Attack] = []
        self.special_abilities: List[SpecialAbility] = []
        
        # Monster stats
        self.damage_resistances: List[str] = []
        self.damage_immunities: List[str] = []
        self.damage_vulnerabilities: List[str] = []
        self.condition_immunities: List[str] = []
        
        # Senses
        self.darkvision = 0
        self.blindsight = 0
        self.tremorsense = 0
        self.truesight = 0
        self.passive_perception = 10
        
        # Languages
        self.languages: List[str] = []
        
    def add_attack(self, attack: Attack):
        """Add an attack to the monster."""
        self.attacks.append(attack)
    
    def add_special_ability(self, ability: SpecialAbility):
        """Add a special ability to the monster."""
        self.special_abilities.append(ability)
    
    def get_available_attacks(self) -> List[Attack]:
        """Get all available attacks."""
        return self.attacks.copy()
    
    def get_available_abilities(self) -> List[SpecialAbility]:
        """Get all usable special abilities."""
        return [ability for ability in self.special_abilities if ability.can_use()]
    
    def calculate_xp_value(self) -> int:
        """Calculate XP value based on challenge rating."""
        xp_table = {
            0: 10, 0.125: 25, 0.25: 50, 0.5: 100,
            1: 200, 2: 450, 3: 700, 4: 1100, 5: 1800,
            6: 2300, 7: 2900, 8: 3900, 9: 5000, 10: 5900,
            11: 7200, 12: 8400, 13: 10000, 14: 11500, 15: 13000,
            16: 15000, 17: 18000, 18: 20000, 19: 22000, 20: 25000,
            21: 33000, 22: 41000, 23: 50000, 24: 62000, 25: 75000,
            26: 90000, 27: 105000, 28: 120000, 29: 135000, 30: 155000
        }
        return xp_table.get(self.challenge_rating, 0)
    
    def has_resistance(self, damage_type: str) -> bool:
        """Check if monster has resistance to damage type."""
        return damage_type.lower() in [r.lower() for r in self.damage_resistances]
    
    def has_immunity(self, damage_type: str) -> bool:
        """Check if monster has immunity to damage type."""
        return damage_type.lower() in [i.lower() for i in self.damage_immunities]
    
    def has_vulnerability(self, damage_type: str) -> bool:
        """Check if monster has vulnerability to damage type."""
        return damage_type.lower() in [v.lower() for v in self.damage_vulnerabilities]
    
    def apply_damage_modifiers(self, damage: int, damage_type: str) -> int:
        """Apply resistance, immunity, and vulnerability to damage."""
        if self.has_immunity(damage_type):
            return 0
        elif self.has_resistance(damage_type):
            return damage // 2
        elif self.has_vulnerability(damage_type):
            return damage * 2
        else:
            return damage
    
    def take_damage(self, damage: int, damage_type: str = "bludgeoning") -> int:
        """Apply damage with type-specific modifiers."""
        modified_damage = self.apply_damage_modifiers(damage, damage_type)
        return super().take_damage(modified_damage)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert monster to dictionary for serialization."""
        base_dict = super().to_dict()
        base_dict.update({
            "challenge_rating": self.challenge_rating,
            "monster_type": self.monster_type.value,
            "alignment": self.alignment,
            "attacks": [attack.to_dict() for attack in self.attacks],
            "special_abilities": [ability.to_dict() for ability in self.special_abilities],
            "damage_resistances": self.damage_resistances,
            "damage_immunities": self.damage_immunities,
            "damage_vulnerabilities": self.damage_vulnerabilities,
            "condition_immunities": self.condition_immunities,
            "darkvision": self.darkvision,
            "blindsight": self.blindsight,
            "tremorsense": self.tremorsense,
            "truesight": self.truesight,
            "passive_perception": self.passive_perception,
            "languages": self.languages
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Monster':
        """Create monster from dictionary."""
        monster = cls(
            name=data["name"],
            challenge_rating=data.get("challenge_rating", 0.125)
        )
        
        # Set base character properties
        monster.level = data.get("level", 1)
        monster.size = Size(data.get("size", "medium"))
        monster.ability_scores = AbilityScores.from_dict(data.get("ability_scores", {}))
        
        # Hit points
        hp_data = data.get("hit_points", {})
        monster.hit_points = HitPoints(
            maximum=hp_data.get("maximum", 10),
            current=hp_data.get("current", 10),
            temporary=hp_data.get("temporary", 0)
        )
        
        # Armor class
        ac_data = data.get("armor_class", {})
        monster.armor_class = ArmorClass(
            base=ac_data.get("base", 10),
            armor_bonus=ac_data.get("armor_bonus", 0),
            shield_bonus=ac_data.get("shield_bonus", 0),
            dex_modifier=ac_data.get("dex_modifier", 0),
            natural_armor=ac_data.get("natural_armor", 0),
            misc_bonus=ac_data.get("misc_bonus", 0)
        )
        
        monster.speed = data.get("speed", 30)
        monster.saving_throws = SavingThrows.from_dict(data.get("saving_throws", {}))
        monster.conditions = ConditionManager.from_dict(data.get("conditions", {}))
        
        # Monster-specific properties
        monster.monster_type = MonsterType(data.get("monster_type", "beast"))
        monster.alignment = data.get("alignment", "neutral")
        
        # Attacks and abilities
        monster.attacks = [Attack.from_dict(attack_data) for attack_data in data.get("attacks", [])]
        monster.special_abilities = [SpecialAbility.from_dict(ability_data) 
                                   for ability_data in data.get("special_abilities", [])]
        
        # Damage modifiers
        monster.damage_resistances = data.get("damage_resistances", [])
        monster.damage_immunities = data.get("damage_immunities", [])
        monster.damage_vulnerabilities = data.get("damage_vulnerabilities", [])
        monster.condition_immunities = data.get("condition_immunities", [])
        
        # Senses
        monster.darkvision = data.get("darkvision", 0)
        monster.blindsight = data.get("blindsight", 0)
        monster.tremorsense = data.get("tremorsense", 0)
        monster.truesight = data.get("truesight", 0)
        monster.passive_perception = data.get("passive_perception", 10)
        monster.languages = data.get("languages", [])
        
        return monster
