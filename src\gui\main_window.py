"""
Main Window for the D&D Combat Simulator
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Optional, List
import threading
import time

from src.core.character import Character
from src.core.monster import Monster
from src.core.combat import CombatEngine, CombatState
from src.ai.combat_ai import AutoCombat
from src.data.sample_characters import get_sample_character_names, create_sample_character
from src.gui.character_dialog import CharacterDialog
from src.gui.monster_dialog import MonsterDialog

class MainWindow:
    """Main application window for the D&D Combat Simulator."""
    
    def __init__(self, root: tk.Tk):
        self.root = root

        # Initialize combat system
        self.combat_engine = CombatEngine()
        self.auto_combat = AutoCombat(self.combat_engine)
        self.characters: List[Character] = []
        self.monsters: List[Monster] = []

        # Combat state
        self.combat_running = False
        self.auto_simulation = False

        self.setup_window()
        self.create_menu()
        self.create_main_interface()
        self.update_combat_display()
        
    def setup_window(self):
        """Configure the main window properties."""
        self.root.title("D&D Combat Simulator")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Center the window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
    def create_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Combat", command=self.new_combat)
        file_menu.add_command(label="Load Character", command=self.load_character)
        file_menu.add_command(label="Save Character", command=self.save_character)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Combat menu
        combat_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Combat", menu=combat_menu)
        combat_menu.add_command(label="Start Combat", command=self.start_combat)
        combat_menu.add_command(label="Pause Combat", command=self.pause_combat)
        combat_menu.add_command(label="Reset Combat", command=self.reset_combat)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
    def create_main_interface(self):
        """Create the main interface layout."""
        # Create main paned window for resizable sections
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel for character/monster setup
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Right panel for combat log and controls
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)
        
    def create_left_panel(self, parent):
        """Create the left panel with character and monster setup."""
        # Notebook for different tabs
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Characters tab
        char_frame = ttk.Frame(notebook)
        notebook.add(char_frame, text="Characters")
        self.create_character_tab(char_frame)
        
        # Monsters tab
        monster_frame = ttk.Frame(notebook)
        notebook.add(monster_frame, text="Monsters")
        self.create_monster_tab(monster_frame)
        
        # Settings tab
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="Settings")
        self.create_settings_tab(settings_frame)
        
    def create_character_tab(self, parent):
        """Create the character management tab."""
        # Character list
        ttk.Label(parent, text="Party Members:").pack(anchor=tk.W, padx=5, pady=5)
        
        char_list_frame = ttk.Frame(parent)
        char_list_frame.pack(fill=tk.BOTH, expand=True, padx=5)
        
        self.char_listbox = tk.Listbox(char_list_frame)
        char_scrollbar = ttk.Scrollbar(char_list_frame, orient=tk.VERTICAL, command=self.char_listbox.yview)
        self.char_listbox.config(yscrollcommand=char_scrollbar.set)
        
        self.char_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        char_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Character buttons
        char_btn_frame = ttk.Frame(parent)
        char_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(char_btn_frame, text="Add Character", command=self.add_character).pack(side=tk.LEFT, padx=2)
        ttk.Button(char_btn_frame, text="Add Sample", command=self.add_sample_character).pack(side=tk.LEFT, padx=2)
        ttk.Button(char_btn_frame, text="Edit Character", command=self.edit_character).pack(side=tk.LEFT, padx=2)
        ttk.Button(char_btn_frame, text="Remove Character", command=self.remove_character).pack(side=tk.LEFT, padx=2)
        
    def create_monster_tab(self, parent):
        """Create the monster management tab."""
        # Monster list
        ttk.Label(parent, text="Encounter Monsters:").pack(anchor=tk.W, padx=5, pady=5)
        
        monster_list_frame = ttk.Frame(parent)
        monster_list_frame.pack(fill=tk.BOTH, expand=True, padx=5)
        
        self.monster_listbox = tk.Listbox(monster_list_frame)
        monster_scrollbar = ttk.Scrollbar(monster_list_frame, orient=tk.VERTICAL, command=self.monster_listbox.yview)
        self.monster_listbox.config(yscrollcommand=monster_scrollbar.set)
        
        self.monster_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monster_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Monster buttons
        monster_btn_frame = ttk.Frame(parent)
        monster_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(monster_btn_frame, text="Add Monster", command=self.add_monster).pack(side=tk.LEFT, padx=2)
        ttk.Button(monster_btn_frame, text="Edit Monster", command=self.edit_monster).pack(side=tk.LEFT, padx=2)
        ttk.Button(monster_btn_frame, text="Remove Monster", command=self.remove_monster).pack(side=tk.LEFT, padx=2)
        
    def create_settings_tab(self, parent):
        """Create the simulation settings tab."""
        settings_frame = ttk.LabelFrame(parent, text="Simulation Settings")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Combat mode selection
        ttk.Label(settings_frame, text="Combat Mode:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.combat_mode = tk.StringVar(value="manual")
        ttk.Radiobutton(settings_frame, text="Manual Control", variable=self.combat_mode, value="manual").grid(row=1, column=0, sticky=tk.W, padx=20)
        ttk.Radiobutton(settings_frame, text="Auto Simulation", variable=self.combat_mode, value="auto").grid(row=2, column=0, sticky=tk.W, padx=20)
        ttk.Radiobutton(settings_frame, text="Step-by-Step", variable=self.combat_mode, value="step").grid(row=3, column=0, sticky=tk.W, padx=20)
        
    def create_right_panel(self, parent):
        """Create the right panel with combat log and controls."""
        # Combat controls
        control_frame = ttk.LabelFrame(parent, text="Combat Controls")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_btn = ttk.Button(btn_frame, text="Start Combat", command=self.start_combat)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.pause_btn = ttk.Button(btn_frame, text="Pause", command=self.pause_combat, state=tk.DISABLED)
        self.pause_btn.pack(side=tk.LEFT, padx=2)
        
        self.reset_btn = ttk.Button(btn_frame, text="Reset", command=self.reset_combat)
        self.reset_btn.pack(side=tk.LEFT, padx=2)

        self.next_turn_btn = ttk.Button(btn_frame, text="Next Turn", command=self.next_turn, state=tk.DISABLED)
        self.next_turn_btn.pack(side=tk.LEFT, padx=2)
        
        # Combat log
        log_frame = ttk.LabelFrame(parent, text="Combat Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.combat_log = tk.Text(log_text_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.combat_log.yview)
        self.combat_log.config(yscrollcommand=log_scrollbar.set)
        
        self.combat_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    # Menu command methods
    def new_combat(self):
        """Start a new combat encounter."""
        self.log_message("Starting new combat encounter...")
        
    def load_character(self):
        """Load a character from file."""
        filename = filedialog.askopenfilename(
            title="Load Character",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.log_message(f"Loading character from {filename}")
            
    def save_character(self):
        """Save a character to file."""
        filename = filedialog.asksaveasfilename(
            title="Save Character",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.log_message(f"Saving character to {filename}")
            
    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo(
            "About D&D Combat Simulator",
            "D&D Combat Simulator v1.0\n\n"
            "A turn-based combat simulator for D&D 2024 rules.\n"
            "Supports both player practice and DM encounter testing."
        )
        
    # Combat control methods
    def start_combat(self):
        """Start the combat simulation."""
        if not self.characters and not self.monsters:
            messagebox.showwarning("No Participants", "Add characters and monsters before starting combat.")
            return

        if not self.characters:
            messagebox.showwarning("No Characters", "Add at least one character to the party.")
            return

        if not self.monsters:
            messagebox.showwarning("No Monsters", "Add at least one monster to the encounter.")
            return

        try:
            # Reset combat engine
            self.combat_engine = CombatEngine()
            self.auto_combat = AutoCombat(self.combat_engine)

            # Add participants
            for character in self.characters:
                self.combat_engine.add_participant(character)
            for monster in self.monsters:
                self.combat_engine.add_participant(monster)

            # Start combat
            self.combat_engine.start_combat()
            self.combat_running = True

            # Update UI
            self.start_btn.config(state=tk.DISABLED)
            self.pause_btn.config(state=tk.NORMAL)

            # Enable next turn button for step mode
            if combat_mode == "step":
                self.next_turn_btn.config(state=tk.NORMAL)

            # Display combat log
            self.display_combat_log()

            # Start auto simulation if selected
            combat_mode = self.combat_mode.get()
            if combat_mode == "auto":
                self.start_auto_simulation()
            elif combat_mode == "step":
                self.log_message("Step-by-step mode: Use 'Next Turn' to advance.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start combat: {str(e)}")

    def pause_combat(self):
        """Pause the combat simulation."""
        if self.combat_running:
            self.combat_engine.pause_combat()
            self.auto_simulation = False
            self.log_message("Combat paused.")
            self.start_btn.config(state=tk.NORMAL)
            self.pause_btn.config(state=tk.DISABLED)

    def reset_combat(self):
        """Reset the combat simulation."""
        self.combat_running = False
        self.auto_simulation = False
        self.combat_engine.reset_combat()

        # Reset character and monster states
        for character in self.characters:
            character.hit_points.current = character.hit_points.maximum
            character.conditions.clear_all()
            character.death_save_successes = 0
            character.death_save_failures = 0

        for monster in self.monsters:
            monster.hit_points.current = monster.hit_points.maximum
            monster.conditions.clear_all()
            monster.death_save_successes = 0
            monster.death_save_failures = 0

        self.log_message("Combat reset.")
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.next_turn_btn.config(state=tk.DISABLED)
        self.clear_log()
        self.update_combat_display()
        
    # Character management methods
    def add_character(self):
        """Add a new character to the party."""
        dialog = CharacterDialog(self.root)
        character = dialog.show()

        if character:
            self.characters.append(character)
            self.char_listbox.insert(tk.END, f"{character.name} (Level {character.level} {character.character_class.value.title()})")
            self.log_message(f"Added character: {character.name}")
            self.update_combat_participants()

    def add_sample_character(self):
        """Add a sample character to the party."""
        sample_names = get_sample_character_names()

        # Create a simple selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Select Sample Character")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (300 // 2)
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (200 // 2)
        dialog.geometry(f"300x200+{x}+{y}")

        ttk.Label(dialog, text="Choose a sample character:").pack(pady=10)

        # Listbox for sample characters
        listbox = tk.Listbox(dialog)
        for name in sample_names:
            listbox.insert(tk.END, name)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        result = [None]

        def on_ok():
            selection = listbox.curselection()
            if selection:
                result[0] = sample_names[selection[0]]
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        # Buttons
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=5)
        ttk.Button(btn_frame, text="OK", command=on_ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)

        # Handle double-click
        listbox.bind("<Double-1>", lambda e: on_ok())

        dialog.wait_window()

        if result[0]:
            try:
                character = create_sample_character(result[0])
                self.characters.append(character)
                self.char_listbox.insert(tk.END, f"{character.name} (Level {character.level} {character.character_class.value.title()})")
                self.log_message(f"Added sample character: {character.name}")
                self.update_combat_participants()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create sample character: {str(e)}")

    def edit_character(self):
        """Edit the selected character."""
        selection = self.char_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a character to edit.")
            return

        index = selection[0]
        character = self.characters[index]

        dialog = CharacterDialog(self.root, character)
        edited_character = dialog.show()

        if edited_character:
            self.characters[index] = edited_character
            self.char_listbox.delete(index)
            self.char_listbox.insert(index, f"{edited_character.name} (Level {edited_character.level} {edited_character.character_class.value.title()})")
            self.log_message(f"Edited character: {edited_character.name}")
            self.update_combat_participants()

    def remove_character(self):
        """Remove the selected character."""
        selection = self.char_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a character to remove.")
            return

        index = selection[0]
        character = self.characters[index]

        if messagebox.askyesno("Confirm Removal", f"Remove character '{character.name}'?"):
            self.characters.pop(index)
            self.char_listbox.delete(index)
            self.log_message(f"Removed character: {character.name}")
            self.update_combat_participants()
            
    # Monster management methods
    def add_monster(self):
        """Add a new monster to the encounter."""
        dialog = MonsterDialog(self.root)
        monsters = dialog.show()

        if monsters:
            for monster in monsters:
                self.monsters.append(monster)
                self.monster_listbox.insert(tk.END, f"{monster.name} (CR {monster.challenge_rating})")
                self.log_message(f"Added monster: {monster.name}")
            self.update_combat_participants()

    def edit_monster(self):
        """Edit the selected monster."""
        selection = self.monster_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a monster to edit.")
            return

        # For now, just show monster details
        index = selection[0]
        monster = self.monsters[index]
        messagebox.showinfo("Monster Details", f"Name: {monster.name}\nCR: {monster.challenge_rating}\nHP: {monster.hit_points.current}/{monster.hit_points.maximum}\nAC: {monster.armor_class.total}")

    def remove_monster(self):
        """Remove the selected monster."""
        selection = self.monster_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a monster to remove.")
            return

        index = selection[0]
        monster = self.monsters[index]

        if messagebox.askyesno("Confirm Removal", f"Remove monster '{monster.name}'?"):
            self.monsters.pop(index)
            self.monster_listbox.delete(index)
            self.log_message(f"Removed monster: {monster.name}")
            self.update_combat_participants()
            
    # Utility methods
    def log_message(self, message: str):
        """Add a message to the combat log."""
        self.combat_log.config(state=tk.NORMAL)
        self.combat_log.insert(tk.END, f"{message}\n")
        self.combat_log.see(tk.END)
        self.combat_log.config(state=tk.DISABLED)
        
    def clear_log(self):
        """Clear the combat log."""
        self.combat_log.config(state=tk.NORMAL)
        self.combat_log.delete(1.0, tk.END)
        self.combat_log.config(state=tk.DISABLED)

    # Combat system integration methods
    def update_combat_participants(self):
        """Update combat engine with current participants."""
        if hasattr(self, 'combat_engine'):
            # Clear existing participants
            self.combat_engine.participants.clear()
            self.combat_engine.initiative_order.clear()

            # Add current participants
            for character in self.characters:
                self.combat_engine.add_participant(character)
            for monster in self.monsters:
                self.combat_engine.add_participant(monster)

    def display_combat_log(self):
        """Display combat engine log in the GUI."""
        if hasattr(self, 'combat_engine'):
            for message in self.combat_engine.combat_log:
                self.log_message(message)
            # Clear the engine log to avoid duplicates
            self.combat_engine.combat_log.clear()

    def update_combat_display(self):
        """Update the combat display with current state."""
        if not hasattr(self, 'combat_engine'):
            return

        # Update character list with current HP
        for i, character in enumerate(self.characters):
            if i < self.char_listbox.size():
                hp_info = f" (HP: {character.hit_points.current}/{character.hit_points.maximum})"
                status = ""
                if character.is_unconscious():
                    status = " [UNCONSCIOUS]"
                elif not character.is_alive():
                    status = " [DEAD]"

                display_text = f"{character.name} (Level {character.level} {character.character_class.value.title()}){hp_info}{status}"
                self.char_listbox.delete(i)
                self.char_listbox.insert(i, display_text)

        # Update monster list with current HP
        for i, monster in enumerate(self.monsters):
            if i < self.monster_listbox.size():
                hp_info = f" (HP: {monster.hit_points.current}/{monster.hit_points.maximum})"
                status = ""
                if monster.is_unconscious():
                    status = " [UNCONSCIOUS]"
                elif not monster.is_alive():
                    status = " [DEAD]"

                display_text = f"{monster.name} (CR {monster.challenge_rating}){hp_info}{status}"
                self.monster_listbox.delete(i)
                self.monster_listbox.insert(i, display_text)

    def start_auto_simulation(self):
        """Start automated combat simulation in a separate thread."""
        self.auto_simulation = True

        def run_simulation():
            try:
                while self.auto_simulation and self.combat_engine.state.value == "active":
                    # Run one turn
                    if not self.auto_combat.run_single_turn():
                        break

                    # Update display on main thread
                    self.root.after(0, self.display_combat_log)
                    self.root.after(0, self.update_combat_display)

                    # Small delay for readability
                    time.sleep(1)

                # Combat ended
                self.root.after(0, self.on_combat_ended)

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Combat simulation error: {str(e)}"))

        # Start simulation thread
        simulation_thread = threading.Thread(target=run_simulation, daemon=True)
        simulation_thread.start()

    def on_combat_ended(self):
        """Handle combat ending."""
        self.combat_running = False
        self.auto_simulation = False
        self.start_btn.config(state=tk.NORMAL)
        self.pause_btn.config(state=tk.DISABLED)
        self.next_turn_btn.config(state=tk.DISABLED)
        self.display_combat_log()
        self.update_combat_display()

    def next_turn(self):
        """Advance to the next turn in step-by-step mode."""
        if not self.combat_running or self.combat_engine.state.value != "active":
            return

        try:
            current_actor = self.combat_engine.get_current_actor()
            if current_actor:
                # For step mode, use AI to make decisions
                if not self.auto_combat.run_single_turn():
                    self.on_combat_ended()
                else:
                    self.display_combat_log()
                    self.update_combat_display()

        except Exception as e:
            messagebox.showerror("Error", f"Error advancing turn: {str(e)}")
