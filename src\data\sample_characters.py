"""
Sample character creation for D&D Combat Simulator
"""

from ..core.character import Character, CharacterClass, Size, HitPoints, ArmorClass
from ..core.abilities import AbilityScores, AbilityType, SavingThrows

def create_sample_fighter() -> Character:
    """Create a sample Level 3 Fighter."""
    fighter = Character("Gareth the Bold", level=3, character_class=CharacterClass.FIGHTER)
    
    # Ability scores (point buy with racial bonuses)
    fighter.ability_scores = AbilityScores(
        strength=16,    # 15 + 1 racial
        dexterity=14,   # 14 base
        constitution=15, # 13 + 2 racial
        intelligence=10, # 10 base
        wisdom=12,      # 12 base
        charisma=8      # 8 base
    )
    
    # Hit points (10 + 2*9 + 3*2 con modifier)
    fighter.hit_points = HitPoints(maximum=34, current=34)
    
    # Armor class (Chain mail + shield)
    fighter.armor_class = ArmorClass(
        base=10,
        armor_bonus=6,  # Chain mail
        shield_bonus=2, # Shield
        dex_modifier=0, # Chain mail limits dex bonus
        misc_bonus=0
    )
    
    fighter.speed = 30
    
    # Saving throw proficiencies (Fighter gets Str and Con)
    proficiencies = {
        AbilityType.STRENGTH: True,
        AbilityType.CONSTITUTION: True
    }
    fighter.saving_throws = SavingThrows(proficiencies)
    
    return fighter

def create_sample_wizard() -> Character:
    """Create a sample Level 3 Wizard."""
    wizard = Character("Elara Starweaver", level=3, character_class=CharacterClass.WIZARD)
    
    # Ability scores
    wizard.ability_scores = AbilityScores(
        strength=8,     # 8 base
        dexterity=14,   # 14 base
        constitution=14, # 12 + 2 racial
        intelligence=16, # 15 + 1 racial
        wisdom=12,      # 12 base
        charisma=10     # 10 base
    )
    
    # Hit points (6 + 2*4 + 3*2 con modifier)
    wizard.hit_points = HitPoints(maximum=20, current=20)
    
    # Armor class (Mage armor spell)
    wizard.armor_class = ArmorClass(
        base=10,
        armor_bonus=3,  # Mage armor
        shield_bonus=0,
        dex_modifier=wizard.get_ability_modifier(AbilityType.DEXTERITY),
        misc_bonus=0
    )
    
    wizard.speed = 30
    
    # Saving throw proficiencies (Wizard gets Int and Wis)
    proficiencies = {
        AbilityType.INTELLIGENCE: True,
        AbilityType.WISDOM: True
    }
    wizard.saving_throws = SavingThrows(proficiencies)
    
    return wizard

def create_sample_rogue() -> Character:
    """Create a sample Level 3 Rogue."""
    rogue = Character("Shadowstep", level=3, character_class=CharacterClass.ROGUE)
    
    # Ability scores
    rogue.ability_scores = AbilityScores(
        strength=10,    # 10 base
        dexterity=16,   # 15 + 1 racial
        constitution=14, # 14 base
        intelligence=12, # 12 base
        wisdom=13,      # 13 base
        charisma=8      # 8 base
    )
    
    # Hit points (8 + 2*6 + 3*2 con modifier)
    rogue.hit_points = HitPoints(maximum=26, current=26)
    
    # Armor class (Studded leather)
    rogue.armor_class = ArmorClass(
        base=10,
        armor_bonus=2,  # Studded leather
        shield_bonus=0,
        dex_modifier=rogue.get_ability_modifier(AbilityType.DEXTERITY),
        misc_bonus=0
    )
    
    rogue.speed = 30
    
    # Saving throw proficiencies (Rogue gets Dex and Int)
    proficiencies = {
        AbilityType.DEXTERITY: True,
        AbilityType.INTELLIGENCE: True
    }
    rogue.saving_throws = SavingThrows(proficiencies)
    
    return rogue

def create_sample_cleric() -> Character:
    """Create a sample Level 3 Cleric."""
    cleric = Character("Brother Marcus", level=3, character_class=CharacterClass.CLERIC)
    
    # Ability scores
    cleric.ability_scores = AbilityScores(
        strength=14,    # 14 base
        dexterity=10,   # 10 base
        constitution=14, # 12 + 2 racial
        intelligence=12, # 12 base
        wisdom=16,      # 15 + 1 racial
        charisma=13     # 13 base
    )
    
    # Hit points (8 + 2*6 + 3*2 con modifier)
    cleric.hit_points = HitPoints(maximum=26, current=26)
    
    # Armor class (Scale mail + shield)
    cleric.armor_class = ArmorClass(
        base=10,
        armor_bonus=4,  # Scale mail
        shield_bonus=2, # Shield
        dex_modifier=0, # Scale mail limits dex bonus
        misc_bonus=0
    )
    
    cleric.speed = 30
    
    # Saving throw proficiencies (Cleric gets Wis and Cha)
    proficiencies = {
        AbilityType.WISDOM: True,
        AbilityType.CHARISMA: True
    }
    cleric.saving_throws = SavingThrows(proficiencies)
    
    return cleric

# Sample character registry
SAMPLE_CHARACTERS = {
    "Fighter (Level 3)": create_sample_fighter,
    "Wizard (Level 3)": create_sample_wizard,
    "Rogue (Level 3)": create_sample_rogue,
    "Cleric (Level 3)": create_sample_cleric,
}

def get_sample_character_names():
    """Get list of available sample character names."""
    return list(SAMPLE_CHARACTERS.keys())

def create_sample_character(name: str) -> Character:
    """Create a sample character by name."""
    if name not in SAMPLE_CHARACTERS:
        raise ValueError(f"Unknown sample character: {name}")
    return SAMPLE_CHARACTERS[name]()

def create_sample_party() -> list[Character]:
    """Create a balanced sample party."""
    return [
        create_sample_fighter(),
        create_sample_wizard(),
        create_sample_rogue(),
        create_sample_cleric()
    ]
