"""
Combat engine for D&D Combat Simulator
"""

from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
import random

from .character import Character
from .monster import Monster, Attack
from .dice import roll_d20, roll_death_save, AdvantageType, DiceRoll
from .abilities import AbilityType
from .conditions import ConditionType, Condition

class CombatState(Enum):
    """States of combat."""
    NOT_STARTED = "not_started"
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"

class ActionType(Enum):
    """Types of actions in combat."""
    ATTACK = "attack"
    CAST_SPELL = "cast_spell"
    DASH = "dash"
    DODGE = "dodge"
    HELP = "help"
    HIDE = "hide"
    READY = "ready"
    SEARCH = "search"
    USE_OBJECT = "use_object"
    DISENGAGE = "disengage"

@dataclass
class CombatAction:
    """Represents an action taken in combat."""
    actor: Character
    action_type: ActionType
    target: Optional[Character] = None
    description: str = ""
    roll_result: Optional[<PERSON><PERSON><PERSON><PERSON>] = None
    damage_dealt: int = 0
    success: bool = True

@dataclass
class CombatRound:
    """Represents a round of combat."""
    round_number: int
    actions: List[CombatAction]
    
    def add_action(self, action: CombatAction):
        """Add an action to this round."""
        self.actions.append(action)

class CombatEngine:
    """Manages D&D combat encounters."""
    
    def __init__(self):
        self.state = CombatState.NOT_STARTED
        self.participants: List[Character] = []
        self.initiative_order: List[Character] = []
        self.current_turn_index = 0
        self.current_round = 1
        self.rounds: List[CombatRound] = []
        self.combat_log: List[str] = []
        
    def add_participant(self, character: Character):
        """Add a character or monster to combat."""
        if character not in self.participants:
            self.participants.append(character)
            
    def remove_participant(self, character: Character):
        """Remove a character from combat."""
        if character in self.participants:
            self.participants.remove(character)
            if character in self.initiative_order:
                self.initiative_order.remove(character)
                
    def start_combat(self):
        """Start the combat encounter."""
        if not self.participants:
            raise ValueError("Cannot start combat with no participants")
            
        self.state = CombatState.ACTIVE
        self.current_round = 1
        self.current_turn_index = 0
        self.rounds = []
        self.combat_log = []
        
        # Roll initiative for all participants
        self.roll_initiative()
        
        # Start first round
        self.start_new_round()
        
        self.log_message(f"Combat started! Round {self.current_round}")
        self.log_message(f"Initiative order: {', '.join([p.name for p in self.initiative_order])}")
        
    def roll_initiative(self):
        """Roll initiative for all participants and sort by result."""
        initiative_results = []
        
        for participant in self.participants:
            initiative = participant.roll_initiative()
            initiative_results.append((participant, initiative))
            self.log_message(f"{participant.name} rolled {initiative} for initiative")
            
        # Sort by initiative (highest first), with dexterity as tiebreaker
        initiative_results.sort(key=lambda x: (x[1], x[0].get_ability_modifier(AbilityType.DEXTERITY)), reverse=True)
        
        self.initiative_order = [participant for participant, _ in initiative_results]
        
    def start_new_round(self):
        """Start a new round of combat."""
        current_round = CombatRound(self.current_round, [])
        self.rounds.append(current_round)
        
        # Reset all participants for the new round
        for participant in self.initiative_order:
            participant.start_turn()
            
    def get_current_actor(self) -> Optional[Character]:
        """Get the character whose turn it is."""
        if not self.initiative_order or self.state != CombatState.ACTIVE:
            return None
        return self.initiative_order[self.current_turn_index]
        
    def next_turn(self):
        """Advance to the next turn."""
        if self.state != CombatState.ACTIVE:
            return
            
        # End current actor's turn
        current_actor = self.get_current_actor()
        if current_actor:
            current_actor.end_turn()
            
        # Advance turn index
        self.current_turn_index += 1
        
        # Check if round is complete
        if self.current_turn_index >= len(self.initiative_order):
            self.end_round()
            
        # Check if combat should end
        if self.should_end_combat():
            self.end_combat()
        else:
            next_actor = self.get_current_actor()
            if next_actor:
                self.log_message(f"{next_actor.name}'s turn")
                
    def end_round(self):
        """End the current round and start the next."""
        self.log_message(f"End of round {self.current_round}")
        
        # Handle end-of-round effects
        for participant in self.initiative_order:
            # Tick condition durations
            participant.conditions.tick_all_durations()
            
            # Handle death saves for unconscious characters
            if participant.is_unconscious() and participant.hit_points.current <= 0:
                self.handle_death_save(participant)
                
        # Start next round
        self.current_round += 1
        self.current_turn_index = 0
        self.start_new_round()
        self.log_message(f"Starting round {self.current_round}")
        
    def handle_death_save(self, character: Character):
        """Handle death saving throws for an unconscious character."""
        if character.death_save_failures >= 3 or character.death_save_successes >= 3:
            return  # Already determined
            
        death_save = roll_death_save()
        self.log_message(f"{character.name} rolls death save: {death_save}")
        
        if death_save.total == 1:
            # Critical failure - 2 failures
            character.death_save_failures += 2
            self.log_message(f"{character.name} critically failed death save! ({character.death_save_failures}/3 failures)")
        elif death_save.total < 10:
            # Failure
            character.death_save_failures += 1
            self.log_message(f"{character.name} failed death save ({character.death_save_failures}/3 failures)")
        elif death_save.total == 20:
            # Critical success - regain 1 HP
            character.heal(1)
            character.death_save_successes = 0
            character.death_save_failures = 0
            self.log_message(f"{character.name} critically succeeded death save and regains 1 HP!")
        else:
            # Success
            character.death_save_successes += 1
            self.log_message(f"{character.name} succeeded death save ({character.death_save_successes}/3 successes)")
            
        # Check if character dies
        if character.death_save_failures >= 3:
            self.log_message(f"{character.name} has died!")
            
    def should_end_combat(self) -> bool:
        """Check if combat should end."""
        alive_participants = [p for p in self.participants if p.is_alive()]
        
        if len(alive_participants) <= 1:
            return True
            
        # Check if all remaining participants are on the same side
        # For now, assume monsters vs non-monsters
        monsters = [p for p in alive_participants if isinstance(p, Monster)]
        characters = [p for p in alive_participants if not isinstance(p, Monster)]
        
        return len(monsters) == 0 or len(characters) == 0
        
    def end_combat(self):
        """End the combat encounter."""
        self.state = CombatState.ENDED
        
        alive_participants = [p for p in self.participants if p.is_alive()]
        monsters = [p for p in alive_participants if isinstance(p, Monster)]
        characters = [p for p in alive_participants if not isinstance(p, Monster)]
        
        if len(monsters) == 0:
            self.log_message("Victory! All monsters defeated!")
        elif len(characters) == 0:
            self.log_message("Defeat! All party members have fallen!")
        else:
            self.log_message("Combat ended!")
            
        self.log_message(f"Combat lasted {self.current_round} rounds")
        
    def pause_combat(self):
        """Pause the combat."""
        if self.state == CombatState.ACTIVE:
            self.state = CombatState.PAUSED
            self.log_message("Combat paused")
            
    def resume_combat(self):
        """Resume paused combat."""
        if self.state == CombatState.PAUSED:
            self.state = CombatState.ACTIVE
            self.log_message("Combat resumed")
            
    def reset_combat(self):
        """Reset combat to initial state."""
        self.state = CombatState.NOT_STARTED
        self.initiative_order.clear()
        self.current_turn_index = 0
        self.current_round = 1
        self.rounds.clear()
        self.combat_log.clear()
        
        # Reset all participants
        for participant in self.participants:
            participant.conditions.clear_all()
            participant.death_save_successes = 0
            participant.death_save_failures = 0
            
    def make_attack(self, attacker: Character, target: Character, attack: Attack) -> CombatAction:
        """Execute an attack action."""
        # Determine advantage/disadvantage
        advantage = AdvantageType.NORMAL
        if attacker.conditions.has_advantage_on_attacks():
            advantage = AdvantageType.ADVANTAGE
        elif attacker.conditions.has_disadvantage_on_attacks():
            advantage = AdvantageType.DISADVANTAGE
            
        if target.conditions.attacks_have_advantage_against():
            if advantage == AdvantageType.DISADVANTAGE:
                advantage = AdvantageType.NORMAL  # Advantage and disadvantage cancel
            else:
                advantage = AdvantageType.ADVANTAGE
        elif target.conditions.attacks_have_disadvantage_against():
            if advantage == AdvantageType.ADVANTAGE:
                advantage = AdvantageType.NORMAL  # Advantage and disadvantage cancel
            else:
                advantage = AdvantageType.DISADVANTAGE
                
        # Make attack roll
        attack_roll = roll_d20(attack.attack_bonus, advantage)
        hit = attack_roll.total >= target.armor_class.total
        
        action = CombatAction(
            actor=attacker,
            action_type=ActionType.ATTACK,
            target=target,
            description=f"{attacker.name} attacks {target.name} with {attack.name}",
            roll_result=attack_roll,
            success=hit
        )
        
        if hit:
            # Roll damage
            from .dice import roll_damage
            damage_roll = roll_damage(attack.damage_dice, attack.damage_bonus)
            damage = damage_roll.total
            
            # Apply damage
            if isinstance(target, Monster):
                actual_damage = target.take_damage(damage, attack.damage_type)
            else:
                actual_damage = target.take_damage(damage)
                
            action.damage_dealt = actual_damage
            action.description += f" - HIT! Deals {actual_damage} {attack.damage_type} damage"
            
            self.log_message(f"{action.description} (AC {target.armor_class.total}, rolled {attack_roll})")
            self.log_message(f"Damage: {damage_roll} = {actual_damage} damage")
        else:
            action.description += " - MISS!"
            self.log_message(f"{action.description} (AC {target.armor_class.total}, rolled {attack_roll})")
            
        # Add to current round
        if self.rounds:
            self.rounds[-1].add_action(action)
            
        return action
        
    def log_message(self, message: str):
        """Add a message to the combat log."""
        self.combat_log.append(message)
        
    def get_combat_summary(self) -> Dict[str, Any]:
        """Get a summary of the combat encounter."""
        return {
            "state": self.state.value,
            "round": self.current_round,
            "participants": [p.name for p in self.participants],
            "initiative_order": [p.name for p in self.initiative_order],
            "current_actor": self.get_current_actor().name if self.get_current_actor() else None,
            "total_rounds": len(self.rounds),
            "log_entries": len(self.combat_log)
        }
