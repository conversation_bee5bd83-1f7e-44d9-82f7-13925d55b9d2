"""
Class-based features, attacks, and spells for D&D 2024
"""

from typing import Dict, List, Set, Optional
from dataclasses import dataclass
from enum import Enum

from src.core.character import CharacterClass
from src.core.abilities import AbilityType
from src.core.weapons import WeaponCategory, get_weapon
from src.core.attacks import create_weapon_attack, create_spell_attack, create_unarmed_strike
from src.core.weapons import DamageType

@dataclass
class ClassFeature:
    """Represents a class feature."""
    name: str
    level: int
    description: str
    uses_per_rest: Optional[int] = None
    rest_type: str = "long"  # "short" or "long"

@dataclass
class SpellcastingInfo:
    """Spellcasting information for a class."""
    spellcasting_ability: AbilityType
    spell_slots_by_level: Dict[int, Dict[int, int]]  # character_level -> {spell_level: slots}
    cantrips_known: Dict[int, int]  # character_level -> cantrips_known
    spells_known: Dict[int, int]  # character_level -> spells_known (for known casters)
    ritual_casting: bool = False
    spellcasting_focus: str = ""

@dataclass
class ClassInfo:
    """Complete class information."""
    name: str
    hit_die: int
    primary_ability: List[AbilityType]
    saving_throw_proficiencies: List[AbilityType]
    weapon_proficiencies: List[WeaponCategory]
    armor_proficiencies: List[str]
    starting_equipment: List[str]
    spellcasting: Optional[SpellcastingInfo] = None
    features_by_level: Dict[int, List[ClassFeature]] = None

# Spell lists by class
CANTRIPS = {
    "wizard": [
        ("Fire Bolt", "1d10", DamageType.FIRE, True, 120),
        ("Ray of Frost", "1d8", DamageType.COLD, True, 60),
        ("Mage Hand", "0", DamageType.FORCE, False, 30),
        ("Prestidigitation", "0", DamageType.FORCE, False, 10),
        ("Light", "0", DamageType.RADIANT, False, 0),
        ("Minor Illusion", "0", DamageType.PSYCHIC, False, 30),
    ],
    "cleric": [
        ("Sacred Flame", "1d8", DamageType.RADIANT, False, 60),
        ("Guidance", "0", DamageType.FORCE, False, 0),
        ("Light", "0", DamageType.RADIANT, False, 0),
        ("Thaumaturgy", "0", DamageType.FORCE, False, 30),
        ("Word of Radiance", "1d6", DamageType.RADIANT, False, 5),
    ],
    "sorcerer": [
        ("Fire Bolt", "1d10", DamageType.FIRE, True, 120),
        ("Ray of Frost", "1d8", DamageType.COLD, True, 60),
        ("Mage Hand", "0", DamageType.FORCE, False, 30),
        ("Prestidigitation", "0", DamageType.FORCE, False, 10),
        ("Light", "0", DamageType.RADIANT, False, 0),
    ],
    "warlock": [
        ("Eldritch Blast", "1d10", DamageType.FORCE, True, 120),
        ("Mage Hand", "0", DamageType.FORCE, False, 30),
        ("Minor Illusion", "0", DamageType.PSYCHIC, False, 30),
        ("Prestidigitation", "0", DamageType.FORCE, False, 10),
    ]
}

# Class definitions
CLASSES = {
    CharacterClass.FIGHTER: ClassInfo(
        name="Fighter",
        hit_die=10,
        primary_ability=[AbilityType.STRENGTH, AbilityType.DEXTERITY],
        saving_throw_proficiencies=[AbilityType.STRENGTH, AbilityType.CONSTITUTION],
        weapon_proficiencies=[WeaponCategory.SIMPLE_MELEE, WeaponCategory.SIMPLE_RANGED, 
                            WeaponCategory.MARTIAL_MELEE, WeaponCategory.MARTIAL_RANGED],
        armor_proficiencies=["Light", "Medium", "Heavy", "Shields"],
        starting_equipment=["Chain Mail", "Shield", "Longsword", "Javelin"],
        features_by_level={
            1: [ClassFeature("Fighting Style", 1, "Choose a fighting style"), 
                ClassFeature("Second Wind", 1, "Regain 1d10+level HP as bonus action", 1, "short")],
            2: [ClassFeature("Action Surge", 2, "Take additional action", 1, "short")],
            3: [ClassFeature("Martial Archetype", 3, "Choose your archetype")],
        }
    ),
    
    CharacterClass.WIZARD: ClassInfo(
        name="Wizard",
        hit_die=6,
        primary_ability=[AbilityType.INTELLIGENCE],
        saving_throw_proficiencies=[AbilityType.INTELLIGENCE, AbilityType.WISDOM],
        weapon_proficiencies=[WeaponCategory.SIMPLE_MELEE, WeaponCategory.SIMPLE_RANGED],
        armor_proficiencies=[],
        starting_equipment=["Dagger", "Spellbook", "Component Pouch"],
        spellcasting=SpellcastingInfo(
            spellcasting_ability=AbilityType.INTELLIGENCE,
            spell_slots_by_level={
                1: {1: 2}, 2: {1: 3}, 3: {1: 4, 2: 2}, 4: {1: 4, 2: 3}, 5: {1: 4, 2: 3, 3: 2}
            },
            cantrips_known={1: 3, 4: 4, 10: 5},
            spells_known={1: 6, 2: 8, 3: 10, 4: 12, 5: 14},
            ritual_casting=True,
            spellcasting_focus="Arcane Focus"
        ),
        features_by_level={
            1: [ClassFeature("Spellcasting", 1, "Cast wizard spells"),
                ClassFeature("Arcane Recovery", 1, "Recover spell slots on short rest", 1, "long")],
            2: [ClassFeature("Arcane Tradition", 2, "Choose your tradition")],
        }
    ),
    
    CharacterClass.ROGUE: ClassInfo(
        name="Rogue",
        hit_die=8,
        primary_ability=[AbilityType.DEXTERITY],
        saving_throw_proficiencies=[AbilityType.DEXTERITY, AbilityType.INTELLIGENCE],
        weapon_proficiencies=[WeaponCategory.SIMPLE_MELEE, WeaponCategory.SIMPLE_RANGED],
        armor_proficiencies=["Light"],
        starting_equipment=["Leather Armor", "Shortsword", "Shortbow", "Thieves' Tools"],
        features_by_level={
            1: [ClassFeature("Expertise", 1, "Double proficiency on chosen skills"),
                ClassFeature("Sneak Attack", 1, "Extra damage when conditions met")],
            2: [ClassFeature("Cunning Action", 2, "Dash, Disengage, or Hide as bonus action")],
            3: [ClassFeature("Roguish Archetype", 3, "Choose your archetype")],
        }
    ),
    
    CharacterClass.CLERIC: ClassInfo(
        name="Cleric",
        hit_die=8,
        primary_ability=[AbilityType.WISDOM],
        saving_throw_proficiencies=[AbilityType.WISDOM, AbilityType.CHARISMA],
        weapon_proficiencies=[WeaponCategory.SIMPLE_MELEE, WeaponCategory.SIMPLE_RANGED],
        armor_proficiencies=["Light", "Medium", "Shields"],
        starting_equipment=["Scale Mail", "Shield", "Mace", "Holy Symbol"],
        spellcasting=SpellcastingInfo(
            spellcasting_ability=AbilityType.WISDOM,
            spell_slots_by_level={
                1: {1: 2}, 2: {1: 3}, 3: {1: 4, 2: 2}, 4: {1: 4, 2: 3}, 5: {1: 4, 2: 3, 3: 2}
            },
            cantrips_known={1: 3, 4: 4, 10: 5},
            spells_known={1: 4, 2: 5, 3: 6, 4: 7, 5: 9},  # Clerics prepare spells
            ritual_casting=True,
            spellcasting_focus="Holy Symbol"
        ),
        features_by_level={
            1: [ClassFeature("Spellcasting", 1, "Cast cleric spells"),
                ClassFeature("Divine Domain", 1, "Choose your domain")],
            2: [ClassFeature("Channel Divinity", 2, "Use divine power", 1, "short")],
        }
    ),
}

def get_class_info(character_class: CharacterClass) -> ClassInfo:
    """Get class information."""
    return CLASSES.get(character_class)

def get_starting_attacks(character_class: CharacterClass, level: int = 1) -> List:
    """Get starting attacks for a class."""
    attacks = []
    class_info = get_class_info(character_class)
    
    if not class_info:
        return attacks
    
    # Add weapon attacks based on starting equipment
    for equipment in class_info.starting_equipment:
        weapon_data = get_weapon(equipment)
        if weapon_data:
            attacks.append(create_weapon_attack(weapon_data))
    
    # Add cantrips for spellcasters
    if class_info.spellcasting:
        class_name = character_class.value
        if class_name in CANTRIPS:
            cantrips_known = class_info.spellcasting.cantrips_known.get(level, 0)
            available_cantrips = CANTRIPS[class_name]
            
            for i, (name, damage, damage_type, is_attack, range_ft) in enumerate(available_cantrips):
                if i < cantrips_known and damage != "0":
                    attacks.append(create_spell_attack(
                        name, damage, damage_type, 
                        spell_attack=is_attack, range_normal=range_ft
                    ))
    
    # Always add unarmed strike
    attacks.append(create_unarmed_strike(level))
    
    return attacks

def get_available_cantrips(character_class: CharacterClass) -> List[tuple]:
    """Get available cantrips for a class."""
    class_name = character_class.value
    return CANTRIPS.get(class_name, [])

def get_weapon_proficiencies(character_class: CharacterClass) -> List[WeaponCategory]:
    """Get weapon proficiencies for a class."""
    class_info = get_class_info(character_class)
    return class_info.weapon_proficiencies if class_info else []
