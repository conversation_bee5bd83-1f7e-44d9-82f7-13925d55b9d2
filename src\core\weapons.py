"""
Weapon system for D&D Combat Simulator
"""

from typing import List, Dict, Optional, Set
from dataclasses import dataclass
from enum import Enum

class WeaponProperty(Enum):
    """Weapon properties from D&D 2024."""
    AMMUNITION = "ammunition"
    FINESSE = "finesse"
    HEAVY = "heavy"
    LIGHT = "light"
    LOADING = "loading"
    RANGE = "range"
    REACH = "reach"
    SPECIAL = "special"
    THROWN = "thrown"
    TWO_HANDED = "two_handed"
    VERSATILE = "versatile"

class DamageType(Enum):
    """Damage types in D&D."""
    ACID = "acid"
    BLUDGEONING = "bludgeoning"
    COLD = "cold"
    FIRE = "fire"
    FORCE = "force"
    LIGHTNING = "lightning"
    NECROTIC = "necrotic"
    PIERCING = "piercing"
    POISON = "poison"
    PSYCHIC = "psychic"
    RADIANT = "radiant"
    SLASHING = "slashing"
    THUNDER = "thunder"

class WeaponCategory(Enum):
    """Weapon categories."""
    SIMPLE_MELEE = "simple_melee"
    SIMPLE_RANGED = "simple_ranged"
    MARTIAL_MELEE = "martial_melee"
    MARTIAL_RANGED = "martial_ranged"

@dataclass
class WeaponData:
    """Complete weapon definition."""
    name: str
    category: WeaponCategory
    damage_dice: str
    damage_type: DamageType
    properties: Set[WeaponProperty]
    cost_gp: int = 0
    weight_lbs: float = 0.0
    
    # Range properties (for ranged weapons)
    range_normal: Optional[int] = None
    range_long: Optional[int] = None
    
    # Versatile damage (for versatile weapons)
    versatile_damage: Optional[str] = None
    
    # Special properties
    special_description: str = ""
    
    def is_melee(self) -> bool:
        """Check if this is a melee weapon."""
        return self.category in [WeaponCategory.SIMPLE_MELEE, WeaponCategory.MARTIAL_MELEE]
    
    def is_ranged(self) -> bool:
        """Check if this is a ranged weapon."""
        return self.category in [WeaponCategory.SIMPLE_RANGED, WeaponCategory.MARTIAL_RANGED]
    
    def is_martial(self) -> bool:
        """Check if this requires martial weapon proficiency."""
        return self.category in [WeaponCategory.MARTIAL_MELEE, WeaponCategory.MARTIAL_RANGED]
    
    def has_property(self, prop: WeaponProperty) -> bool:
        """Check if weapon has a specific property."""
        return prop in self.properties

# Weapon Database
WEAPONS_DB: Dict[str, WeaponData] = {
    # Simple Melee Weapons
    "Club": WeaponData(
        name="Club",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d4",
        damage_type=DamageType.BLUDGEONING,
        properties={WeaponProperty.LIGHT},
        cost_gp=1,
        weight_lbs=2.0
    ),
    
    "Dagger": WeaponData(
        name="Dagger",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d4",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.FINESSE, WeaponProperty.LIGHT, WeaponProperty.THROWN},
        range_normal=20,
        range_long=60,
        cost_gp=2,
        weight_lbs=1.0
    ),
    
    "Handaxe": WeaponData(
        name="Handaxe",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.SLASHING,
        properties={WeaponProperty.LIGHT, WeaponProperty.THROWN},
        range_normal=20,
        range_long=60,
        cost_gp=5,
        weight_lbs=2.0
    ),
    
    "Javelin": WeaponData(
        name="Javelin",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.THROWN},
        range_normal=30,
        range_long=120,
        cost_gp=5,
        weight_lbs=2.0
    ),
    
    "Mace": WeaponData(
        name="Mace",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.BLUDGEONING,
        properties=set(),
        cost_gp=5,
        weight_lbs=4.0
    ),
    
    "Quarterstaff": WeaponData(
        name="Quarterstaff",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.BLUDGEONING,
        properties={WeaponProperty.VERSATILE},
        versatile_damage="1d8",
        cost_gp=2,
        weight_lbs=4.0
    ),
    
    "Spear": WeaponData(
        name="Spear",
        category=WeaponCategory.SIMPLE_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.THROWN, WeaponProperty.VERSATILE},
        versatile_damage="1d8",
        range_normal=20,
        range_long=60,
        cost_gp=1,
        weight_lbs=3.0
    ),
    
    # Simple Ranged Weapons
    "Light Crossbow": WeaponData(
        name="Light Crossbow",
        category=WeaponCategory.SIMPLE_RANGED,
        damage_dice="1d8",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.AMMUNITION, WeaponProperty.LOADING, WeaponProperty.TWO_HANDED},
        range_normal=80,
        range_long=320,
        cost_gp=25,
        weight_lbs=5.0
    ),
    
    "Shortbow": WeaponData(
        name="Shortbow",
        category=WeaponCategory.SIMPLE_RANGED,
        damage_dice="1d6",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.AMMUNITION, WeaponProperty.TWO_HANDED},
        range_normal=80,
        range_long=320,
        cost_gp=25,
        weight_lbs=2.0
    ),
    
    # Martial Melee Weapons
    "Battleaxe": WeaponData(
        name="Battleaxe",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="1d8",
        damage_type=DamageType.SLASHING,
        properties={WeaponProperty.VERSATILE},
        versatile_damage="1d10",
        cost_gp=10,
        weight_lbs=4.0
    ),
    
    "Greatsword": WeaponData(
        name="Greatsword",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="2d6",
        damage_type=DamageType.SLASHING,
        properties={WeaponProperty.HEAVY, WeaponProperty.TWO_HANDED},
        cost_gp=50,
        weight_lbs=6.0
    ),
    
    "Longsword": WeaponData(
        name="Longsword",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="1d8",
        damage_type=DamageType.SLASHING,
        properties={WeaponProperty.VERSATILE},
        versatile_damage="1d10",
        cost_gp=15,
        weight_lbs=3.0
    ),
    
    "Rapier": WeaponData(
        name="Rapier",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="1d8",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.FINESSE},
        cost_gp=25,
        weight_lbs=2.0
    ),
    
    "Scimitar": WeaponData(
        name="Scimitar",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.SLASHING,
        properties={WeaponProperty.FINESSE, WeaponProperty.LIGHT},
        cost_gp=25,
        weight_lbs=3.0
    ),
    
    "Shortsword": WeaponData(
        name="Shortsword",
        category=WeaponCategory.MARTIAL_MELEE,
        damage_dice="1d6",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.FINESSE, WeaponProperty.LIGHT},
        cost_gp=10,
        weight_lbs=2.0
    ),
    
    # Martial Ranged Weapons
    "Heavy Crossbow": WeaponData(
        name="Heavy Crossbow",
        category=WeaponCategory.MARTIAL_RANGED,
        damage_dice="1d10",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.AMMUNITION, WeaponProperty.HEAVY, WeaponProperty.LOADING, WeaponProperty.TWO_HANDED},
        range_normal=100,
        range_long=400,
        cost_gp=50,
        weight_lbs=18.0
    ),
    
    "Longbow": WeaponData(
        name="Longbow",
        category=WeaponCategory.MARTIAL_RANGED,
        damage_dice="1d8",
        damage_type=DamageType.PIERCING,
        properties={WeaponProperty.AMMUNITION, WeaponProperty.HEAVY, WeaponProperty.TWO_HANDED},
        range_normal=150,
        range_long=600,
        cost_gp=50,
        weight_lbs=2.0
    ),
}

def get_weapon(name: str) -> Optional[WeaponData]:
    """Get weapon data by name."""
    return WEAPONS_DB.get(name)

def get_weapons_by_category(category: WeaponCategory) -> List[WeaponData]:
    """Get all weapons of a specific category."""
    return [weapon for weapon in WEAPONS_DB.values() if weapon.category == category]

def get_simple_weapons() -> List[WeaponData]:
    """Get all simple weapons."""
    return [weapon for weapon in WEAPONS_DB.values() 
            if weapon.category in [WeaponCategory.SIMPLE_MELEE, WeaponCategory.SIMPLE_RANGED]]

def get_martial_weapons() -> List[WeaponData]:
    """Get all martial weapons."""
    return [weapon for weapon in WEAPONS_DB.values() 
            if weapon.category in [WeaponCategory.MARTIAL_MELEE, WeaponCategory.MARTIAL_RANGED]]

def get_all_weapon_names() -> List[str]:
    """Get list of all weapon names."""
    return list(WEAPONS_DB.keys())
